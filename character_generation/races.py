"""
Race definitions for One Life Isekai character generation
"""

RACES = {
    'human': {
        'name': 'Human',
        'rarity': 'common',
        'quality': 'average',
        'base_stats': {
            'STR': (15, 25),
            'DEX': (15, 25),
            'VIT': (15, 25),
            'INT': (15, 25),
            'SPE': (15, 25),
            'WILL': (15, 25)
        },
        'utility_stats': {
            'height': (160, 190),
            'beauty': (80, 120),
            'estimated_lifespan': (60, 80),
            'load_capacity_bonus': 0,
            'magic_affinity': (10, 30)
        },
        'innate_traits': [],
        'bonus_chances': {
            'coordination_tier_up': 5,
            'magic_affinity_tier_up': 5
        },
        'description': 'asdasd'
    },
    'elf': {
        'name': 'Elf',
        'rarity': 'rare',
        'quality': 'positive',
        'base_stats': {
            'STR': (20, 30),
            'DEX': (45, 60),
            'VIT': (25, 35),
            'INT': (50, 70),
            'SPE': (35, 45),
            'WILL': (30, 40)
        },
        'utility_stats': {
            'height': (180, 210),
            'beauty': (90, 130),
            'estimated_lifespan': (120, 145),
            'load_capacity_bonus': 20,
            'magic_affinity': (40, 70)
        },
        'innate_traits': ['natural_beauty'],
        'bonus_chances': {
            'coordination_tier_up': 10,
            'magic_affinity_tier_up': 10
        },
        'description': 'asdasd'
    },
    'dwarf': {
        'name': 'Dwarf',
        'rarity': 'uncommon',
        'quality': 'positive',
        'base_stats': {
            'STR': (40, 55),
            'DEX': (20, 30),
            'VIT': (45, 60),
            'INT': (25, 35),
            'SPE': (10, 20),
            'WILL': (35, 50)
        },
        'utility_stats': {
            'height': (120, 150),
            'beauty': (60, 100),
            'estimated_lifespan': (90, 120),
            'load_capacity_bonus': 50,
            'magic_affinity': (5, 25)
        },
        'innate_traits': ['iron_constitution'],
        'bonus_chances': {
            'physical_condition_tier_up': 15,
            'immune_system_tier_up': 10
        },
        'description': 'asdasd'
    },
    'halfling': {
        'name': 'Halfling',
        'rarity': 'uncommon',
        'quality': 'average',
        'base_stats': {
            'STR': (10, 20),
            'DEX': (30, 45),
            'VIT': (20, 30),
            'INT': (20, 30),
            'SPE': (25, 35),
            'WILL': (25, 40)
        },
        'utility_stats': {
            'height': (90, 120),
            'beauty': (70, 110),
            'estimated_lifespan': (70, 90),
            'load_capacity_bonus': -20,
            'magic_affinity': (15, 35)
        },
        'innate_traits': ['lucky'],
        'bonus_chances': {
            'perception_tier_up': 15,
            'persuasion_tier_up': 10
        },
        'description': 'asdasd'
    },
    'tiefling': {
        'name': 'Tiefling',
        'rarity': 'rare',
        'quality': 'negative',
        'base_stats': {
            'STR': (25, 35),
            'DEX': (30, 40),
            'VIT': (20, 30),
            'INT': (35, 50),
            'SPE': (25, 35),
            'WILL': (40, 60)
        },
        'utility_stats': {
            'height': (170, 200),
            'beauty': (40, 80),
            'estimated_lifespan': (80, 100),
            'load_capacity_bonus': 10,
            'magic_affinity': (50, 80)
        },
        'innate_traits': ['infernal_heritage'],
        'bonus_chances': {
            'magic_affinity_tier_up': 20,
            'chaos_resistance_bonus': 15
        },
        'description': 'asdasd'
    },
    'dragonborn': {
        'name': 'Dragonborn',
        'rarity': 'very_rare',
        'quality': 'positive',
        'base_stats': {
            'STR': (50, 70),
            'DEX': (20, 30),
            'VIT': (40, 55),
            'INT': (30, 45),
            'SPE': (20, 30),
            'WILL': (45, 65)
        },
        'utility_stats': {
            'height': (190, 220),
            'beauty': (60, 120),
            'estimated_lifespan': (100, 130),
            'load_capacity_bonus': 40,
            'magic_affinity': (30, 60)
        },
        'innate_traits': ['draconic_heritage'],
        'bonus_chances': {
            'physical_condition_tier_up': 20,
            'elemental_resistance_bonus': 25
        },
        'description': 'asdasd'
    },
    'genasi': {
        'name': 'Genasi',
        'rarity': 'very_rare',
        'quality': 'average',
        'base_stats': {
            'STR': (20, 35),
            'DEX': (25, 40),
            'VIT': (25, 40),
            'INT': (30, 45),
            'SPE': (30, 45),
            'WILL': (35, 55)
        },
        'utility_stats': {
            'height': (165, 195),
            'beauty': (70, 130),
            'estimated_lifespan': (85, 110),
            'load_capacity_bonus': 15,
            'magic_affinity': (40, 70)
        },
        'innate_traits': ['elemental_affinity'],
        'bonus_chances': {
            'magic_affinity_tier_up': 15,
            'elemental_mastery_bonus': 20
        },
        'description': 'asdasd'
    },
    'giant_kin': {
        'name': 'Giant-Kin',
        'rarity': 'epic',
        'quality': 'positive',
        'base_stats': {
            'STR': (70, 90),
            'DEX': (10, 20),
            'VIT': (60, 80),
            'INT': (15, 25),
            'SPE': (15, 25),
            'WILL': (30, 45)
        },
        'utility_stats': {
            'height': (220, 280),
            'beauty': (40, 80),
            'estimated_lifespan': (120, 150),
            'load_capacity_bonus': 100,
            'magic_affinity': (5, 20)
        },
        'innate_traits': ['giant_blood'],
        'bonus_chances': {
            'physical_condition_tier_up': 30,
            'height_tier_up': 50
        },
        'description': 'asdasd'
    },
    'void_touched': {
        'name': 'Void-Touched',
        'rarity': 'legendary',
        'quality': 'negative',
        'base_stats': {
            'STR': (15, 25),
            'DEX': (35, 50),
            'VIT': (10, 20),
            'INT': (60, 80),
            'SPE': (40, 55),
            'WILL': (70, 90)
        },
        'utility_stats': {
            'height': (160, 190),
            'beauty': (20, 60),
            'estimated_lifespan': (40, 70),
            'load_capacity_bonus': -10,
            'magic_affinity': (80, 100)
        },
        'innate_traits': ['void_touched', 'arcane_blood'],
        'bonus_chances': {
            'magic_affinity_tier_up': 40,
            'chaos_mastery_bonus': 30,
            'madness_chance': 20
        },
        'description': 'asdasd'
    },
    'celestial_born': {
        'name': 'Celestial-Born',
        'rarity': 'legendary',
        'quality': 'positive',
        'base_stats': {
            'STR': (30, 45),
            'DEX': (40, 55),
            'VIT': (35, 50),
            'INT': (50, 70),
            'SPE': (35, 50),
            'WILL': (60, 80)
        },
        'utility_stats': {
            'height': (175, 205),
            'beauty': (140, 200),
            'estimated_lifespan': (150, 200),
            'load_capacity_bonus': 30,
            'magic_affinity': (60, 90)
        },
        'innate_traits': ['blessed', 'divine_heritage'],
        'bonus_chances': {
            'beauty_tier_up': 30,
            'holy_mastery_bonus': 40,
            'divine_protection': 25
        },
        'description': 'asdasd'
    }
}

RACE_RARITY_WEIGHTS = {
    'common': 100,
    'uncommon': 50,
    'rare': 20,
    'very_rare': 8,
    'epic': 3,
    'legendary': 1
}

RACE_QUALITY_DESCRIPTORS = {
    'negative': '🔻 Negative',
    'average': '⚪ Average',
    'positive': '🔺 Positive'
}
