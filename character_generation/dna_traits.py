"""
DNA trait definitions for One Life Isekai character generation
"""

DNA_TRAIT_CATEGORIES = {
    'physical_condition': {
        'name': 'Physical Condition',
        'icon': '💪',
        'traits': {
            'disabled': {
                'name': 'Disabled',
                'rarity': 'very_rare',
                'quality': 'negative',
                'range': (1, 5),
                'effects': {'STR': -75, 'DEX': -25, 'SPE': -50},
                'weight': 1
            },
            'undersized': {
                'name': 'Undersized',
                'rarity': 'rare',
                'quality': 'negative',
                'range': (6, 15),
                'effects': {'STR': -40, 'DEX': -10, 'SPE': -20},
                'weight': 3
            },
            'delicate': {
                'name': 'Delicate',
                'rarity': 'uncommon',
                'quality': 'negative',
                'range': (16, 30),
                'effects': {'STR': -20, 'DEX': -5, 'SPE': -15},
                'weight': 8
            },
            'below_average': {
                'name': 'Below Average',
                'rarity': 'common',
                'quality': 'negative',
                'range': (31, 45),
                'effects': {'STR': -10},
                'weight': 15
            },
            'average': {
                'name': 'Average',
                'rarity': 'common',
                'quality': 'average',
                'range': (46, 70),
                'effects': {},
                'weight': 40
            },
            'robust': {
                'name': 'Robust',
                'rarity': 'common',
                'quality': 'positive',
                'range': (71, 85),
                'effects': {'STR': 10},
                'weight': 15
            },
            'hardy': {
                'name': 'Hardy',
                'rarity': 'uncommon',
                'quality': 'positive',
                'range': (86, 92),
                'effects': {'STR': 20},
                'weight': 8
            },
            'gifted': {
                'name': 'Gifted',
                'rarity': 'rare',
                'quality': 'positive',
                'range': (93, 97),
                'effects': {'STR': 30, 'DEX': 5, 'SPE': 5},
                'weight': 3
            },
            'primordial': {
                'name': 'Primordial',
                'rarity': 'very_rare',
                'quality': 'positive',
                'range': (98, 99),
                'effects': {'STR': 40, 'DEX': 10, 'SPE': 10},
                'weight': 1
            },
            'transcendent': {
                'name': 'Transcendent',
                'rarity': 'legendary',
                'quality': 'positive',
                'range': (100, 100),
                'effects': {'STR': 50, 'DEX': 15, 'SPE': 15},
                'weight': 0.1
            }
        }
    },
    'coordination': {
        'name': 'Coordination',
        'icon': '🤸',
        'traits': {
            'coordination_disabled': {
                'name': 'Coordination Disabled',
                'rarity': 'very_rare',
                'quality': 'negative',
                'range': (1, 5),
                'effects': {'STR': -15, 'DEX': -50, 'SPE': -50},
                'weight': 1
            },
            'clumsy': {
                'name': 'Clumsy',
                'rarity': 'rare',
                'quality': 'negative',
                'range': (6, 15),
                'effects': {'STR': -5, 'DEX': -35, 'SPE': -15},
                'weight': 3
            },
            'slow_handed': {
                'name': 'Slow Handed',
                'rarity': 'uncommon',
                'quality': 'negative',
                'range': (16, 30),
                'effects': {'DEX': -20, 'SPE': -10},
                'weight': 8
            },
            'stiff': {
                'name': 'Stiff',
                'rarity': 'common',
                'quality': 'negative',
                'range': (31, 45),
                'effects': {'DEX': -10},
                'weight': 15
            },
            'average': {
                'name': 'Average',
                'rarity': 'common',
                'quality': 'average',
                'range': (46, 70),
                'effects': {},
                'weight': 40
            },
            'steady': {
                'name': 'Steady',
                'rarity': 'common',
                'quality': 'positive',
                'range': (71, 85),
                'effects': {'DEX': 10},
                'weight': 15
            },
            'nimble': {
                'name': 'Nimble',
                'rarity': 'uncommon',
                'quality': 'positive',
                'range': (86, 92),
                'effects': {'DEX': 20, 'SPE': 5},
                'weight': 8
            },
            'graceful': {
                'name': 'Graceful',
                'rarity': 'rare',
                'quality': 'positive',
                'range': (93, 97),
                'effects': {'DEX': 30, 'SPE': 10},
                'weight': 3
            },
            'preternatural': {
                'name': 'Preternatural',
                'rarity': 'very_rare',
                'quality': 'positive',
                'range': (98, 99),
                'effects': {'STR': 10, 'DEX': 40, 'SPE': 15},
                'weight': 1
            },
            'otherworldly': {
                'name': 'Otherworldly',
                'rarity': 'legendary',
                'quality': 'positive',
                'range': (100, 100),
                'effects': {'STR': 15, 'DEX': 50, 'SPE': 20},
                'weight': 0.1
            }
        }
    },
    'beauty': {
        'name': 'Beauty',
        'icon': '✨',
        'traits': {
            'deformed': {
                'name': 'Deformed',
                'rarity': 'very_rare',
                'quality': 'negative',
                'range': (1, 19),
                'effects': {'beauty': (1, 19)},
                'weight': 1
            },
            'ugly': {
                'name': 'Ugly',
                'rarity': 'rare',
                'quality': 'negative',
                'range': (20, 49),
                'effects': {'beauty': (20, 49)},
                'weight': 3
            },
            'plain': {
                'name': 'Plain',
                'rarity': 'uncommon',
                'quality': 'negative',
                'range': (50, 69),
                'effects': {'beauty': (50, 69)},
                'weight': 8
            },
            'rough': {
                'name': 'Rough',
                'rarity': 'common',
                'quality': 'negative',
                'range': (70, 89),
                'effects': {'beauty': (70, 89)},
                'weight': 15
            },
            'average': {
                'name': 'Average',
                'rarity': 'common',
                'quality': 'average',
                'range': (90, 109),
                'effects': {'beauty': (90, 109)},
                'weight': 40
            },
            'pleasant': {
                'name': 'Pleasant',
                'rarity': 'common',
                'quality': 'positive',
                'range': (110, 129),
                'effects': {'beauty': (110, 129)},
                'weight': 15
            },
            'attractive': {
                'name': 'Attractive',
                'rarity': 'uncommon',
                'quality': 'positive',
                'range': (130, 159),
                'effects': {'beauty': (130, 159)},
                'weight': 8
            },
            'beautiful': {
                'name': 'Beautiful',
                'rarity': 'rare',
                'quality': 'positive',
                'range': (160, 179),
                'effects': {'beauty': (160, 179)},
                'weight': 3
            },
            'stunning': {
                'name': 'Stunning',
                'rarity': 'very_rare',
                'quality': 'positive',
                'range': (180, 199),
                'effects': {'beauty': (180, 199)},
                'weight': 1
            },
            'divine': {
                'name': 'Divine',
                'rarity': 'legendary',
                'quality': 'positive',
                'range': (200, 250),
                'effects': {'beauty': (200, 250)},
                'weight': 0.1
            }
        }
    },
    'immune_system': {
        'name': 'Immune System',
        'icon': '🛡️',
        'traits': {
            'immunodeficient': {
                'name': 'Immunodeficient',
                'rarity': 'very_rare',
                'quality': 'negative',
                'range': (1, 19),
                'effects': {'immune_system': (1, 19), 'all_resistances': -50},
                'weight': 1
            },
            'frail_immunity': {
                'name': 'Frail Immunity',
                'rarity': 'rare',
                'quality': 'negative',
                'range': (20, 49),
                'effects': {'immune_system': (20, 49), 'all_resistances': -30},
                'weight': 3
            },
            'susceptible': {
                'name': 'Susceptible',
                'rarity': 'uncommon',
                'quality': 'negative',
                'range': (50, 69),
                'effects': {'immune_system': (50, 69), 'all_resistances': -20},
                'weight': 8
            },
            'weak': {
                'name': 'Weak',
                'rarity': 'common',
                'quality': 'negative',
                'range': (70, 89),
                'effects': {'immune_system': (70, 89), 'all_resistances': -10},
                'weight': 15
            },
            'average': {
                'name': 'Average',
                'rarity': 'common',
                'quality': 'average',
                'range': (90, 109),
                'effects': {'immune_system': (90, 109)},
                'weight': 40
            },
            'resilient': {
                'name': 'Resilient',
                'rarity': 'common',
                'quality': 'positive',
                'range': (110, 129),
                'effects': {'immune_system': (110, 129), 'all_resistances': 5},
                'weight': 15
            },
            'hardy': {
                'name': 'Hardy',
                'rarity': 'uncommon',
                'quality': 'positive',
                'range': (130, 159),
                'effects': {'immune_system': (130, 159), 'all_resistances': 10},
                'weight': 8
            },
            'resistant_blooded': {
                'name': 'Resistant-Blooded',
                'rarity': 'rare',
                'quality': 'positive',
                'range': (160, 179),
                'effects': {'immune_system': (160, 179), 'all_resistances': 15},
                'weight': 3
            },
            'plagueproof': {
                'name': 'Plagueproof',
                'rarity': 'very_rare',
                'quality': 'positive',
                'range': (180, 199),
                'effects': {'immune_system': (180, 199), 'all_resistances': 20},
                'weight': 1
            },
            'immune_ascendant': {
                'name': 'Immune Ascendant',
                'rarity': 'legendary',
                'quality': 'positive',
                'range': (200, 249),
                'effects': {'immune_system': (200, 249), 'all_resistances': 25},
                'weight': 0.1
            }
        }
    },
    'perception': {
        'name': 'Perception',
        'icon': '👁️',
        'traits': {
            'oblivious': {
                'name': 'Oblivious',
                'rarity': 'very_rare',
                'quality': 'negative',
                'range': (1, 19),
                'effects': {'perception': (1, 19)},
                'weight': 1
            },
            'dull_sensed': {
                'name': 'Dull-Sensed',
                'rarity': 'rare',
                'quality': 'negative',
                'range': (20, 49),
                'effects': {'perception': (20, 49)},
                'weight': 3
            },
            'foggy': {
                'name': 'Foggy',
                'rarity': 'uncommon',
                'quality': 'negative',
                'range': (50, 69),
                'effects': {'perception': (50, 69)},
                'weight': 8
            },
            'instinct_poor': {
                'name': 'Instinct-Poor',
                'rarity': 'common',
                'quality': 'negative',
                'range': (70, 89),
                'effects': {'perception': (70, 89)},
                'weight': 15
            },
            'average': {
                'name': 'Average',
                'rarity': 'common',
                'quality': 'average',
                'range': (90, 109),
                'effects': {'perception': (90, 109)},
                'weight': 40
            },
            'aware': {
                'name': 'Aware',
                'rarity': 'common',
                'quality': 'positive',
                'range': (110, 129),
                'effects': {'perception': (110, 129)},
                'weight': 15
            },
            'attuned': {
                'name': 'Attuned',
                'rarity': 'uncommon',
                'quality': 'positive',
                'range': (130, 159),
                'effects': {'perception': (130, 159)},
                'weight': 8
            },
            'foresensed': {
                'name': 'Foresensed',
                'rarity': 'rare',
                'quality': 'positive',
                'range': (160, 179),
                'effects': {'perception': (160, 179)},
                'weight': 3
            },
            'seers_blood': {
                'name': "Seer's Blood",
                'rarity': 'very_rare',
                'quality': 'positive',
                'range': (180, 199),
                'effects': {'perception': (180, 199)},
                'weight': 1
            },
            'empyrean_sense': {
                'name': 'Empyrean Sense',
                'rarity': 'legendary',
                'quality': 'positive',
                'range': (200, 250),
                'effects': {'perception': (200, 250)},
                'weight': 0.1
            }
        }
    },
    'iq': {
        'name': 'Intelligence Quotient',
        'icon': '🧠',
        'traits': {
            'mentally_retarded': {
                'name': 'Mentally Retarded',
                'rarity': 'very_rare',
                'quality': 'negative',
                'range': (1, 39),
                'effects': {'iq': (1, 39)},
                'weight': 1
            },
            'dim_witted': {
                'name': 'Dim Witted',
                'rarity': 'rare',
                'quality': 'negative',
                'range': (40, 59),
                'effects': {'iq': (40, 59)},
                'weight': 3
            },
            'dull': {
                'name': 'Dull',
                'rarity': 'uncommon',
                'quality': 'negative',
                'range': (60, 69),
                'effects': {'iq': (60, 69)},
                'weight': 8
            },
            'below_average': {
                'name': 'Below Average',
                'rarity': 'common',
                'quality': 'negative',
                'range': (70, 95),
                'effects': {'iq': (70, 95)},
                'weight': 15
            },
            'average': {
                'name': 'Average',
                'rarity': 'common',
                'quality': 'average',
                'range': (96, 104),
                'effects': {'iq': (96, 104)},
                'weight': 40
            },
            'bright': {
                'name': 'Bright',
                'rarity': 'common',
                'quality': 'positive',
                'range': (105, 114),
                'effects': {'iq': (105, 114)},
                'weight': 15
            },
            'clever': {
                'name': 'Clever',
                'rarity': 'uncommon',
                'quality': 'positive',
                'range': (115, 124),
                'effects': {'iq': (115, 124)},
                'weight': 8
            },
            'brilliant': {
                'name': 'Brilliant',
                'rarity': 'rare',
                'quality': 'positive',
                'range': (125, 134),
                'effects': {'iq': (125, 134)},
                'weight': 3
            },
            'genius': {
                'name': 'Genius',
                'rarity': 'very_rare',
                'quality': 'positive',
                'range': (135, 150),
                'effects': {'iq': (135, 150)},
                'weight': 1
            },
            'transcendent_mind': {
                'name': 'Transcendent Mind',
                'rarity': 'legendary',
                'quality': 'positive',
                'range': (151, 200),
                'effects': {'iq': (151, 200)},
                'weight': 0.1
            }
        }
    },
    'persuasion': {
        'name': 'Persuasion',
        'icon': '🗣️',
        'traits': {
            'socially_deficient': {
                'name': 'Socially Deficient',
                'rarity': 'very_rare',
                'quality': 'negative',
                'range': (1, 19),
                'effects': {'persuasion': (1, 19)},
                'weight': 1
            },
            'awkward': {
                'name': 'Awkward',
                'rarity': 'rare',
                'quality': 'negative',
                'range': (20, 49),
                'effects': {'persuasion': (20, 49)},
                'weight': 3
            },
            'flat': {
                'name': 'Flat',
                'rarity': 'uncommon',
                'quality': 'negative',
                'range': (50, 69),
                'effects': {'persuasion': (50, 69)},
                'weight': 8
            },
            'blunt': {
                'name': 'Blunt',
                'rarity': 'common',
                'quality': 'negative',
                'range': (70, 89),
                'effects': {'persuasion': (70, 89)},
                'weight': 15
            },
            'neutral': {
                'name': 'Neutral',
                'rarity': 'common',
                'quality': 'average',
                'range': (90, 109),
                'effects': {'persuasion': (90, 109)},
                'weight': 40
            },
            'clear_spoken': {
                'name': 'Clear-Spoken',
                'rarity': 'common',
                'quality': 'positive',
                'range': (110, 129),
                'effects': {'persuasion': (110, 129)},
                'weight': 15
            },
            'charming': {
                'name': 'Charming',
                'rarity': 'uncommon',
                'quality': 'positive',
                'range': (130, 159),
                'effects': {'persuasion': (130, 159)},
                'weight': 8
            },
            'influential': {
                'name': 'Influential',
                'rarity': 'rare',
                'quality': 'positive',
                'range': (160, 179),
                'effects': {'persuasion': (160, 179)},
                'weight': 3
            },
            'compelling': {
                'name': 'Compelling',
                'rarity': 'very_rare',
                'quality': 'positive',
                'range': (180, 199),
                'effects': {'persuasion': (180, 199)},
                'weight': 1
            },
            'silver_tongued': {
                'name': 'Silver Tongued',
                'rarity': 'legendary',
                'quality': 'positive',
                'range': (200, 250),
                'effects': {'persuasion': (200, 250)},
                'weight': 0.1
            }
        }
    },
    'magic_affinity': {
        'name': 'Magic Affinity',
        'icon': '🔮',
        'traits': {
            'magic_blind': {
                'name': 'Magic Blind',
                'rarity': 'common',
                'quality': 'negative',
                'range': (1, 39),
                'effects': {'magic_affinity': (1, 39)},
                'elements': [],  # No elemental affinity
                'weight': 40
            },
            'resistant': {
                'name': 'Resistant',
                'rarity': 'uncommon',
                'quality': 'negative',
                'range': (40, 59),
                'effects': {'magic_affinity': (40, 59), 'WILL': 20},
                'elements': 1,  # 1 random element
                'weight': 15
            },
            'tuned': {
                'name': 'Tuned',
                'rarity': 'common',
                'quality': 'negative',
                'range': (60, 69),
                'effects': {'magic_affinity': (60, 69), 'WILL': 40},
                'elements': 1,  # 1 random element
                'weight': 25
            },
            'aligned': {
                'name': 'Aligned',
                'rarity': 'common',
                'quality': 'average',
                'range': (70, 95),
                'effects': {'magic_affinity': (70, 95), 'WILL': 80},
                'elements': 1,  # 1 random element
                'weight': 15
            },
            'gifted': {
                'name': 'Gifted',
                'rarity': 'uncommon',
                'quality': 'positive',
                'range': (96, 104),
                'effects': {'magic_affinity': (96, 104), 'WILL': 150},
                'elements': 2,  # 2 random elements
                'weight': 8
            },
            'arcane_touched': {
                'name': 'Arcane-Touched',
                'rarity': 'rare',
                'quality': 'positive',
                'range': (105, 124),
                'effects': {'magic_affinity': (105, 124), 'WILL': 300},
                'elements': 3,  # 3 random elements
                'weight': 3
            },
            'mythic_vessel': {
                'name': 'Mythic Vessel',
                'rarity': 'legendary',
                'quality': 'positive',
                'range': (125, 200),
                'effects': {'magic_affinity': (125, 200), 'WILL': 500},
                'elements': 4,  # 4 random elements
                'weight': 0.1
            }
        }
    },
    'height': {
        'name': 'Height',
        'icon': '📏',
        'traits': {
            'dwarf_statured': {
                'name': 'Dwarf-Statured',
                'rarity': 'very_rare',
                'quality': 'negative',
                'range': (-30, -14),
                'effects': {'height_modifier': (-30, -14), 'SPE': -20},
                'weight': 1
            },
            'very_short': {
                'name': 'Very Short',
                'rarity': 'rare',
                'quality': 'negative',
                'range': (-13, -9),
                'effects': {'height_modifier': (-13, -9), 'SPE': -10},
                'weight': 3
            },
            'short': {
                'name': 'Short',
                'rarity': 'uncommon',
                'quality': 'negative',
                'range': (-8, -5),
                'effects': {'height_modifier': (-8, -5), 'SPE': -3},
                'weight': 8
            },
            'below_average': {
                'name': 'Below Average',
                'rarity': 'common',
                'quality': 'negative',
                'range': (-4, -3),
                'effects': {'height_modifier': (-4, -3), 'SPE': -1},
                'weight': 15
            },
            'average': {
                'name': 'Average',
                'rarity': 'common',
                'quality': 'average',
                'range': (-2, 2),
                'effects': {'height_modifier': (-2, 2)},
                'weight': 40
            },
            'above_average': {
                'name': 'Above Average',
                'rarity': 'common',
                'quality': 'positive',
                'range': (3, 4),
                'effects': {'height_modifier': (3, 4), 'SPE': 1},
                'weight': 15
            },
            'tall': {
                'name': 'Tall',
                'rarity': 'uncommon',
                'quality': 'positive',
                'range': (5, 8),
                'effects': {'height_modifier': (5, 8), 'SPE': 3, 'STR': 1},
                'weight': 8
            },
            'very_tall': {
                'name': 'Very Tall',
                'rarity': 'rare',
                'quality': 'positive',
                'range': (9, 13),
                'effects': {'height_modifier': (9, 13), 'SPE': 5, 'DEX': -3, 'STR': 3, 'VIT': 1},
                'weight': 3
            },
            'towering': {
                'name': 'Towering',
                'rarity': 'very_rare',
                'quality': 'positive',
                'range': (14, 25),
                'effects': {'height_modifier': (14, 25), 'SPE': 10, 'DEX': -5, 'STR': 5, 'VIT': 5},
                'weight': 1
            },
            'gigantic': {
                'name': 'Gigantic',
                'rarity': 'legendary',
                'quality': 'positive',
                'range': (26, 40),
                'effects': {'height_modifier': (26, 40), 'SPE': 20, 'DEX': -10, 'STR': 10, 'VIT': 10},
                'weight': 0.1
            }
        }
    }
}

GENDER_OPTIONS = {
    'male': {
        'name': 'Male',
        'icon': '♂️',
        'weight': 499,
        'effects': {'STR': 25}
    },
    'female': {
        'name': 'Female',
        'icon': '♀️',
        'weight': 499,
        'effects': {'beauty': 20, 'perception': 10, 'persuasion': 20, 'estimated_lifespan': 6}
    },
    'intersex': {
        'name': 'Intersex',
        'icon': '⚧️',
        'weight': 2,
        'effects': {'STR': 10, 'beauty': 10, 'perception': 5}
    }
}

RARITY_DESCRIPTORS = {
    'common': 'Common',
    'uncommon': 'Uncommon',
    'rare': 'Rare',
    'very_rare': 'Very Rare',
    'epic': 'Epic',
    'legendary': 'Legendary'
}

QUALITY_DESCRIPTORS = {
    'negative': '🔻 Negative',
    'average': '',
    'positive': '🔺 Positive'
}

# DNA Trait Tier Mappings
DNA_TRAIT_TIERS = {
    'physical_condition': {
        'disabled': -4,
        'undersized': -3,
        'delicate': -2,
        'below_average': -1,
        'average': 0,
        'robust': 1,
        'hardy': 2,
        'athletic': 3,
        'powerful': 4,
        'superhuman': 5
    },
    'coordination': {
        'clumsy': -3,
        'uncoordinated': -2,
        'below_average': -1,
        'average': 0,
        'coordinated': 1,
        'agile': 2,
        'graceful': 3,
        'acrobatic': 4
    },
    'beauty': {
        'deformed': -4,
        'ugly': -3,
        'plain': -2,
        'rough': -1,
        'average': 0,
        'attractive': 1,
        'beautiful': 2,
        'stunning': 3,
        'divine': 4
    },
    'immune_system': {
        'compromised': -3,
        'weak': -2,
        'below_average': -1,
        'average': 0,
        'strong': 1,
        'robust': 2,
        'iron_constitution': 3
    },
    'perception': {
        'oblivious': -3,
        'unaware': -2,
        'below_average': -1,
        'average': 0,
        'alert': 1,
        'keen': 2,
        'sharp': 3,
        'eagle_eyed': 4
    },
    'iq': {
        'mentally_retarded': -4,
        'dim_witted': -3,
        'dull': -2,
        'below_average': -1,
        'average': 0,
        'bright': 1,
        'intelligent': 2,
        'brilliant': 3,
        'genius': 4,
        'transcendent': 5
    },
    'persuasion': {
        'repulsive': -3,
        'off_putting': -2,
        'below_average': -1,
        'average': 0,
        'charming': 1,
        'charismatic': 2,
        'magnetic': 3,
        'legendary_presence': 4
    },
    'magic_affinity': {
        'magic_blind': 0,  # Special case - Tier 0 for Magic Blind
        'resistant': 1,
        'tuned': 2,
        'aligned': 3,
        'gifted': 4,
        'arcane_touched': 5,
        'mythic_vessel': 6
    },
    'height': {
        'dwarfism': -4,
        'very_short': -3,
        'short': -2,
        'below_average': -1,
        'average': 0,
        'above_average': 1,
        'tall': 2,
        'very_tall': 3,
        'towering': 4,
        'gigantic': 5
    }
}

def get_dna_trait_tier(category: str, trait_name: str) -> int:
    """Get the tier number for a DNA trait"""
    category_tiers = DNA_TRAIT_TIERS.get(category, {})
    return category_tiers.get(trait_name, 0)

def format_tier_display(tier: int) -> str:
    """Format tier number for display"""
    if tier == 0:
        return "tier 0"
    elif tier > 0:
        return f"tier {tier}"
    else:
        return f"tier {tier}"

# Magic Elements for affinity system
MAGIC_ELEMENTS = {
    'fire': {
        'name': 'Fire',
        'icon': '🔥',
        'damage_stat': 'fire_damage_bonus',
        'resist_stat': 'fire_resist_bonus'
    },
    'water': {
        'name': 'Water',
        'icon': '💧',
        'damage_stat': 'water_damage_bonus',
        'resist_stat': 'water_resist_bonus'
    },
    'earth': {
        'name': 'Earth',
        'icon': '🌍',
        'damage_stat': 'earth_damage_bonus',
        'resist_stat': 'earth_resist_bonus'
    },
    'air': {
        'name': 'Air',
        'icon': '💨',
        'damage_stat': 'air_damage_bonus',
        'resist_stat': 'air_resist_bonus'
    },
    'light': {
        'name': 'Light',
        'icon': '✨',
        'damage_stat': 'light_damage_bonus',
        'resist_stat': 'light_resist_bonus'
    },
    'dark': {
        'name': 'Dark',
        'icon': '🌑',
        'damage_stat': 'dark_damage_bonus',
        'resist_stat': 'dark_resist_bonus'
    },
    'nature': {
        'name': 'Nature',
        'icon': '🌿',
        'damage_stat': 'nature_damage_bonus',
        'resist_stat': 'nature_resist_bonus'
    },
    'arcane': {
        'name': 'Arcane',
        'icon': '🔮',
        'damage_stat': 'arcane_damage_bonus',
        'resist_stat': 'arcane_resist_bonus'
    }
}
