"""
Gothic-themed character generator UI for One Life Isekai
Integrates with the main game menu system
"""

import pygame
import time
import math
import random
from typing import Dict, Any, Optional, List
from .character_generator import CharacterGenerator
from utils.constants import COLORS, SCREEN_WIDTH, SCREEN_HEIGHT
from utils.audio_manager import audio_manager


class CharacterGeneratorUI:
    def __init__(self, screen: pygame.Surface, font_medium: pygame.font.Font, font_small: pygame.font.Font):
        self.screen = screen
        self.font_large = pygame.font.Font(None, 36)
        self.font_medium = font_medium
        self.font_small = font_small
        self.font_title = pygame.font.Font(None, 48)
        
        self.generator = CharacterGenerator()
        self.character = None
        
        # Animation state
        self.fade_alpha = 0
        self.glow_intensity = 0
        self.text_reveal_progress = 0

        # Text effect state
        self.text_effects = {}
        self.last_sound_time = 0
        
        # Colors for gothic theme
        self.parchment_color = (240, 230, 210)
        self.ink_color = (40, 30, 20)
        self.gold_color = (218, 165, 32)
        self.blood_red = (139, 0, 0)
        self.shadow_color = (20, 15, 25)
    
    def create_parchment_background(self, rect: pygame.Rect) -> pygame.Surface:
        """Create a parchment-textured background"""
        surface = pygame.Surface((rect.width, rect.height))
        surface.fill(self.parchment_color)
        
        # Add subtle texture
        for i in range(0, rect.width, 4):
            for j in range(0, rect.height, 4):
                if (i + j) % 8 == 0:
                    color_variation = (
                        max(200, self.parchment_color[0] - 10),
                        max(180, self.parchment_color[1] - 15),
                        max(160, self.parchment_color[2] - 20)
                    )
                    pygame.draw.rect(surface, color_variation, (i, j, 2, 2))
        
        # Add border
        pygame.draw.rect(surface, self.ink_color, surface.get_rect(), 3)
        
        return surface
    
    def draw_glowing_border(self, rect: pygame.Rect, intensity: float = 1.0):
        """Draw a glowing border effect"""
        # Ensure intensity is within valid range
        intensity = max(0.0, min(1.0, intensity))

        glow_color = (
            max(0, min(255, int(self.gold_color[0] * intensity))),
            max(0, min(255, int(self.gold_color[1] * intensity))),
            max(0, min(255, int(self.gold_color[2] * intensity)))
        )

        # Draw multiple border layers for glow effect
        for i in range(5):
            alpha = int(50 * intensity * (1 - i/5))
            if alpha > 0 and alpha <= 255:
                try:
                    glow_rect = pygame.Rect(rect.x - i, rect.y - i, rect.width + i*2, rect.height + i*2)
                    pygame.draw.rect(self.screen, glow_color, glow_rect, 2)
                except (ValueError, TypeError):
                    # Skip this layer if there's a color error
                    continue
    
    def animate_text_reveal(self, text: str, x: int, y: int, font: pygame.font.Font,
                           color: tuple, progress: float) -> None:
        """Animate text appearing with spectacular effects"""
        chars_to_show = int(len(text) * progress)
        visible_text = text[:chars_to_show]

        if visible_text:
            # Play sound effect for new characters
            current_time = time.time()
            if chars_to_show > 0 and current_time - self.last_sound_time > 0.1:
                audio_manager.play_sound('text_reveal', 0.3)
                self.last_sound_time = current_time

            try:
                # Spectacular text effects: transparency + size changes
                # Transparency: fade in effect
                if progress < 0.8:
                    alpha = int(255 * (progress / 0.8))
                else:
                    alpha = int(255 * (1.0 - (progress - 0.8) * 0.5))  # Slight fade at end

                # Size effect: grow then shrink slightly
                if progress < 0.5:
                    scale = 0.7 + (progress * 0.6)  # 0.7 -> 1.0
                else:
                    scale = 1.0 + (0.5 - progress) * 0.3  # 1.0 -> 0.85

                # Create scaled font
                base_size = font.get_height()
                new_size = max(12, int(base_size * scale))
                scaled_font = pygame.font.Font(None, new_size)

                # Ensure color values are valid
                safe_color = (
                    max(0, min(255, int(color[0]))),
                    max(0, min(255, int(color[1]))),
                    max(0, min(255, int(color[2])))
                )

                text_surface = scaled_font.render(visible_text, True, safe_color)
                text_surface.set_alpha(alpha)
                self.screen.blit(text_surface, (x, y))

            except (ValueError, TypeError, IndexError):
                # Fallback to white text if color is invalid
                text_surface = font.render(visible_text, True, (255, 255, 255))
                self.screen.blit(text_surface, (x, y))
    
    def display_dramatic_pause(self, duration: float, message: str = ""):
        """Display a dramatic pause with optional message"""
        start_time = time.time()

        while time.time() - start_time < duration:
            self.screen.fill(self.shadow_color)

            if message:
                # Pulsing text effect
                pulse = (math.sin((time.time() - start_time) * 3) + 1) / 2
                alpha = int(150 + 105 * pulse)

                text_surface = self.font_medium.render(message, True, self.gold_color)
                text_surface.set_alpha(alpha)
                text_rect = text_surface.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2))
                self.screen.blit(text_surface, text_rect)

            pygame.display.flip()
            pygame.time.wait(50)

            # Handle events to prevent freezing
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return False

        return True

    def wait_for_keypress(self, message: str = "Press any key to continue..."):
        """Wait for any key press before continuing"""
        waiting = True

        while waiting:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return False
                elif event.type == pygame.KEYDOWN or event.type == pygame.MOUSEBUTTONDOWN:
                    waiting = False
                    break

            # Display message
            self.screen.fill(self.shadow_color, (0, SCREEN_HEIGHT - 60, SCREEN_WIDTH, 60))

            # Pulsing continue message
            pulse = (math.sin(time.time() * 4) + 1) / 2
            alpha = int(100 + 155 * pulse)

            text_surface = self.font_small.render(message, True, self.gold_color)
            text_surface.set_alpha(alpha)
            text_rect = text_surface.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT - 30))
            self.screen.blit(text_surface, text_rect)

            pygame.display.flip()
            pygame.time.wait(50)

        return True
    
    def display_race_reveal(self, race_data: Dict[str, Any]) -> bool:
        """Display dramatic race reveal with gothic styling"""
        if not self.display_dramatic_pause(2.0, "🔮 Consulting the Fates..."):
            return False
        
        # Create parchment scroll
        scroll_rect = pygame.Rect(100, 50, SCREEN_WIDTH-200, SCREEN_HEIGHT-100)
        parchment = self.create_parchment_background(scroll_rect)
        
        # Animation loop
        start_time = time.time()
        animation_duration = 4.0
        
        while time.time() - start_time < animation_duration:
            progress = (time.time() - start_time) / animation_duration
            
            self.screen.fill(self.shadow_color)
            
            # Draw glowing border
            glow_intensity = min(1.0, progress * 2)
            self.draw_glowing_border(scroll_rect, glow_intensity)
            
            # Draw parchment
            parchment_alpha = int(255 * min(1.0, progress * 1.5))
            parchment.set_alpha(parchment_alpha)
            self.screen.blit(parchment, scroll_rect)
            
            if progress > 0.3:
                # Title
                title_progress = min(1.0, (progress - 0.3) / 0.2)
                self.animate_text_reveal(
                    "🔮 Origin Revealed...", 
                    scroll_rect.x + 20, scroll_rect.y + 20,
                    self.font_title, self.blood_red, title_progress
                )
            
            if progress > 0.5:
                # Rarity and quality - UPPERCASE as requested
                rarity_text = f"🔸 Rarity: {race_data['rarity'].upper()}"
                quality_text = ""
                if race_data['quality'] != 'average':
                    quality_indicator = "🔺" if race_data['quality'] == 'positive' else "🔻"
                    quality_text = f" ({quality_indicator} {race_data['quality'].upper()})"
                else:
                    quality_text = " (no bonus)"

                full_rarity_text = rarity_text + quality_text
                
                rarity_progress = min(1.0, (progress - 0.5) / 0.2)
                self.animate_text_reveal(
                    full_rarity_text,
                    scroll_rect.x + 20, scroll_rect.y + 80,
                    self.font_medium, self.gold_color, rarity_progress
                )
            
            if progress > 0.7:
                # Race name
                race_progress = min(1.0, (progress - 0.7) / 0.2)
                race_text = f"🧝 Race: {race_data['name']}"
                self.animate_text_reveal(
                    race_text,
                    scroll_rect.x + 20, scroll_rect.y + 120,
                    self.font_large, self.ink_color, race_progress
                )
            
            if progress > 0.9:
                # Stats
                stats_progress = min(1.0, (progress - 0.9) / 0.1)
                y_offset = 170

                # Display all base stats
                for stat, value in race_data['base_stats'].items():
                    quality = self.evaluate_stat_quality(value)
                    stat_text = f"{stat}: {value} ({quality})"
                    self.animate_text_reveal(
                        stat_text,
                        scroll_rect.x + 20, scroll_rect.y + y_offset,
                        self.font_small, self.ink_color, stats_progress
                    )
                    y_offset += 25

                # Add separator line
                if stats_progress > 0.5:
                    separator_text = "─" * 30
                    self.animate_text_reveal(
                        separator_text,
                        scroll_rect.x + 20, scroll_rect.y + y_offset,
                        self.font_small, self.ink_color, stats_progress
                    )
                    y_offset += 30

                    # Display utility stats
                    for stat, value in race_data['utility_stats'].items():
                        if stat == 'height':
                            utility_text = f"Height: {value} cm"
                        elif stat == 'beauty':
                            utility_text = f"Beauty: {value}"
                        elif stat == 'estimated_lifespan':
                            utility_text = f"Lifespan: {value} years"
                        elif stat == 'load_capacity_bonus' and value != 0:
                            utility_text = f"Load Capacity Bonus: {value:+d} kg"
                        elif stat == 'magic_affinity':
                            utility_text = f"Magic Affinity: {value}"
                        else:
                            continue

                        self.animate_text_reveal(
                            utility_text,
                            scroll_rect.x + 20, scroll_rect.y + y_offset,
                            self.font_small, self.ink_color, stats_progress
                        )
                        y_offset += 25
            
            pygame.display.flip()
            pygame.time.wait(50)
            
            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return False
        
        # Hold final result and wait for keypress
        return self.wait_for_keypress("Press any key to continue...")
    
    def display_gender_reveal(self, gender_data: Dict[str, Any]) -> bool:
        """Display dramatic gender reveal"""
        if not self.display_dramatic_pause(1.5, "⚧️ Determining Physical Form..."):
            return False
        
        # Create smaller parchment for gender
        scroll_rect = pygame.Rect(200, 200, SCREEN_WIDTH-400, 300)
        parchment = self.create_parchment_background(scroll_rect)
        
        start_time = time.time()
        animation_duration = 3.0
        
        while time.time() - start_time < animation_duration:
            progress = (time.time() - start_time) / animation_duration
            
            self.screen.fill(self.shadow_color)
            
            # Draw glowing border
            self.draw_glowing_border(scroll_rect, progress)
            
            # Draw parchment
            parchment.set_alpha(int(255 * min(1.0, progress * 2)))
            self.screen.blit(parchment, scroll_rect)
            
            if progress > 0.3:
                title_progress = min(1.0, (progress - 0.3) / 0.2)
                self.animate_text_reveal(
                    "Your physical identity is...",
                    scroll_rect.x + 20, scroll_rect.y + 30,
                    self.font_medium, self.ink_color, title_progress
                )
            
            if progress > 0.6:
                gender_progress = min(1.0, (progress - 0.6) / 0.2)
                gender_text = f"{gender_data['icon']} {gender_data['name']}"
                self.animate_text_reveal(
                    gender_text,
                    scroll_rect.x + 20, scroll_rect.y + 80,
                    self.font_large, self.gold_color, gender_progress
                )
            
            if progress > 0.8:
                effects_progress = min(1.0, (progress - 0.8) / 0.2)
                effects_text = self.format_gender_effects(gender_data['effects'])
                self.animate_text_reveal(
                    f"Bonuses: {effects_text}",
                    scroll_rect.x + 20, scroll_rect.y + 130,
                    self.font_small, self.ink_color, effects_progress
                )
            
            pygame.display.flip()
            pygame.time.wait(50)
            
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return False
        
        return self.wait_for_keypress("Press any key to continue...")
    
    def display_dna_trait_reveal(self, trait_data: Dict[str, Any]) -> bool:
        """Display dramatic DNA trait reveal"""
        if not self.display_dramatic_pause(0.8, f"🧬 Analyzing {trait_data['category_name']}..."):
            return False
        
        # Create trait scroll
        scroll_rect = pygame.Rect(150, 150, SCREEN_WIDTH-300, 400)
        parchment = self.create_parchment_background(scroll_rect)
        
        start_time = time.time()
        animation_duration = 2.5
        
        while time.time() - start_time < animation_duration:
            progress = (time.time() - start_time) / animation_duration
            
            self.screen.fill(self.shadow_color)
            
            # Intense glow for DNA traits
            glow_intensity = min(1.5, progress * 2)
            self.draw_glowing_border(scroll_rect, glow_intensity)
            
            parchment.set_alpha(int(255 * min(1.0, progress * 2)))
            self.screen.blit(parchment, scroll_rect)
            
            if progress > 0.2:
                # Rarity first - UPPERCASE as requested
                rarity_progress = min(1.0, (progress - 0.2) / 0.2)
                rarity_text = f"📌 {trait_data['rarity'].upper()}"
                self.animate_text_reveal(
                    rarity_text,
                    scroll_rect.x + 20, scroll_rect.y + 30,
                    self.font_medium, self.gold_color, rarity_progress
                )
            
            if progress > 0.4:
                # Quality indicator - UPPERCASE and show "no bonus" for average
                quality_progress = min(1.0, (progress - 0.4) / 0.2)
                if trait_data['quality'] != 'average':
                    quality_indicator = f"🔺 {trait_data['quality'].upper()}"
                    quality_color = self.gold_color if trait_data['quality'] == 'positive' else self.blood_red
                else:
                    quality_indicator = "no bonus"
                    quality_color = self.ink_color

                self.animate_text_reveal(
                    quality_indicator,
                    scroll_rect.x + 20, scroll_rect.y + 70,
                    self.font_medium, quality_color, quality_progress
                )
            
            if progress > 0.6:
                # Trait name and effects
                trait_progress = min(1.0, (progress - 0.6) / 0.3)
                effects_text = self.format_trait_effects(trait_data['effects'])
                trait_text = f"{trait_data['category_icon']} {trait_data['category_name']}: {trait_data['name']}"

                self.animate_text_reveal(
                    trait_text,
                    scroll_rect.x + 20, scroll_rect.y + 110,
                    self.font_medium, self.ink_color, trait_progress
                )

                if effects_text:
                    self.animate_text_reveal(
                        f"Effects: {effects_text}",
                        scroll_rect.x + 20, scroll_rect.y + 150,
                        self.font_small, self.ink_color, trait_progress
                    )

                # Show magic elements if present
                if trait_data.get('magic_elements'):
                    from .dna_traits import MAGIC_ELEMENTS
                    elements_display = []
                    for element in trait_data['magic_elements']:
                        element_data = MAGIC_ELEMENTS[element]
                        elements_display.append(f"{element_data['icon']} {element_data['name']}")

                    self.animate_text_reveal(
                        f"🔮 Elemental Affinities: {', '.join(elements_display)}",
                        scroll_rect.x + 20, scroll_rect.y + 190,
                        self.font_small, self.gold_color, trait_progress
                    )
            
            pygame.display.flip()
            pygame.time.wait(50)
            
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return False
        
        return self.wait_for_keypress("Press any key to continue...")
    
    def evaluate_stat_quality(self, value: int) -> str:
        """Evaluate stat quality"""
        if value >= 50:
            return "Positive"
        elif value >= 30:
            return "Average"
        else:
            return "Negative"
    
    def format_gender_effects(self, effects: Dict[str, Any]) -> str:
        """Format gender effects for display"""
        effect_strings = []
        for stat, value in effects.items():
            if isinstance(value, (int, float)) and 0 < value < 1:
                effect_strings.append(f"+{value:.0%} {stat.title()}")
            else:
                effect_strings.append(f"+{value} {stat.title()}")
        return ", ".join(effect_strings)
    
    def format_trait_effects(self, effects: Dict[str, Any]) -> str:
        """Format trait effects for display"""
        effect_strings = []
        for stat, value in effects.items():
            if isinstance(value, (int, float)) and abs(value) < 1:
                effect_strings.append(f"{value:+.0%} {stat.title()}")
            else:
                effect_strings.append(f"{value:+d} {stat.title()}")
        return ", ".join(effect_strings)

    def display_background_history(self, background_history: List[Dict[str, Any]]) -> bool:
        """Display background history as a scrolling parchment"""
        if not background_history:
            return True

        # Group events by life stage
        stages = {}
        for event in background_history:
            stage = event['life_stage']
            if stage not in stages:
                stages[stage] = []
            stages[stage].append(event)

        current_stage_index = 0
        stage_keys = list(stages.keys())

        while current_stage_index < len(stage_keys):
            stage_name = stage_keys[current_stage_index]
            stage_events = stages[stage_name]

            # Display stage
            if not self.display_life_stage(stage_name, stage_events):
                return False

            current_stage_index += 1

        return True

    def display_life_stage(self, stage_name: str, events: List[Dict[str, Any]]) -> bool:
        """Display a single life stage with its events"""
        # Create large parchment scroll
        scroll_rect = pygame.Rect(50, 50, SCREEN_WIDTH-100, SCREEN_HEIGHT-100)
        parchment = self.create_parchment_background(scroll_rect)

        start_time = time.time()
        animation_duration = 3.0 + len(events) * 0.5  # Longer for more events

        while time.time() - start_time < animation_duration:
            progress = (time.time() - start_time) / animation_duration

            self.screen.fill(self.shadow_color)

            # Draw glowing border
            self.draw_glowing_border(scroll_rect, progress)

            # Draw parchment
            parchment.set_alpha(int(255 * min(1.0, progress * 2)))
            self.screen.blit(parchment, scroll_rect)

            # Stage title
            if progress > 0.1:
                title_progress = min(1.0, (progress - 0.1) / 0.2)
                stage_title = f"📜 {stage_name.upper()}"
                self.animate_text_reveal(
                    stage_title,
                    scroll_rect.x + 20, scroll_rect.y + 20,
                    self.font_large, self.blood_red, title_progress
                )

            # Events
            y_offset = 80
            event_start_progress = 0.3

            for i, event in enumerate(events):
                event_progress_start = event_start_progress + (i * 0.1)
                if progress > event_progress_start:
                    event_progress = min(1.0, (progress - event_progress_start) / 0.15)

                    # Event text
                    age_text = f"Age {event['age']}: {event['event']}"
                    self.animate_text_reveal(
                        age_text,
                        scroll_rect.x + 30, scroll_rect.y + y_offset,
                        self.font_medium, self.ink_color, event_progress
                    )
                    y_offset += 30

                    # Effects (if any)
                    if event.get('effects') and event['type'] != 'growth':
                        effects_text = self.format_event_effects(event['effects'])
                        if effects_text:
                            self.animate_text_reveal(
                                f"  Effects: {effects_text}",
                                scroll_rect.x + 40, scroll_rect.y + y_offset,
                                self.font_small, self.gold_color, event_progress
                            )
                            y_offset += 25

                    # Traits gained
                    if event.get('traits_gained'):
                        traits_text = ", ".join(event['traits_gained'])
                        self.animate_text_reveal(
                            f"  Traits: {traits_text}",
                            scroll_rect.x + 40, scroll_rect.y + y_offset,
                            self.font_small, self.blood_red, event_progress
                        )
                        y_offset += 25

                    # Skills gained
                    if event.get('skills_gained'):
                        skills_text = ", ".join([f"{s['name']} (Lv{s.get('level', 1)})" for s in event['skills_gained']])
                        self.animate_text_reveal(
                            f"  Skills: {skills_text}",
                            scroll_rect.x + 40, scroll_rect.y + y_offset,
                            self.font_small, self.gold_color, event_progress
                        )
                        y_offset += 25

                    y_offset += 10  # Space between events

            pygame.display.flip()
            pygame.time.wait(50)

            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return False

        # Wait for keypress before next stage
        return self.wait_for_keypress(f"Press any key to continue...")

    def format_event_effects(self, effects: Dict[str, Any]) -> str:
        """Format event effects for display"""
        effect_strings = []
        for stat, value in effects.items():
            effect_strings.append(f"{value:+d} {stat}")
        return ", ".join(effect_strings)

    def display_character_summary(self, character: Dict[str, Any]) -> bool:
        """Display scrollable character summary with all generated data"""
        if not self.display_dramatic_pause(2.0, "📜 Compiling your life's chronicle..."):
            return False

        # Create large scrollable parchment
        scroll_rect = pygame.Rect(50, 50, SCREEN_WIDTH-100, SCREEN_HEIGHT-100)
        parchment = self.create_parchment_background(scroll_rect)

        # Prepare all text content
        content_lines = self.prepare_character_summary_content(character)

        # Scrolling variables
        scroll_offset = 0
        line_height = 25
        visible_lines = (scroll_rect.height - 100) // line_height
        max_scroll = max(0, len(content_lines) - visible_lines)

        scrolling = True
        while scrolling:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_UP or event.key == pygame.K_w:
                        scroll_offset = max(0, scroll_offset - 1)
                    elif event.key == pygame.K_DOWN or event.key == pygame.K_s:
                        scroll_offset = min(max_scroll, scroll_offset + 1)
                    elif event.key == pygame.K_SPACE or event.key == pygame.K_RETURN:
                        scrolling = False
                elif event.type == pygame.MOUSEBUTTONDOWN:
                    if event.button == 4:  # Mouse wheel up
                        scroll_offset = max(0, scroll_offset - 3)
                    elif event.button == 5:  # Mouse wheel down
                        scroll_offset = min(max_scroll, scroll_offset + 3)

            # Draw background
            self.screen.fill(self.shadow_color)

            # Draw glowing border
            self.draw_glowing_border(scroll_rect, 1.0)

            # Draw parchment
            self.screen.blit(parchment, scroll_rect)

            # Draw title
            title_surface = self.font_title.render("📜 CHARACTER CHRONICLE", True, self.blood_red)
            title_rect = title_surface.get_rect(centerx=scroll_rect.centerx, y=scroll_rect.y + 20)
            self.screen.blit(title_surface, title_rect)

            # Draw content with scrolling
            content_y = scroll_rect.y + 80
            for i in range(visible_lines):
                line_index = scroll_offset + i
                if line_index < len(content_lines):
                    line_data = content_lines[line_index]
                    text_surface = line_data['font'].render(line_data['text'], True, line_data['color'])
                    self.screen.blit(text_surface, (scroll_rect.x + 20, content_y + i * line_height))

            # Draw scroll indicators
            if scroll_offset > 0:
                up_text = self.font_small.render("▲ Scroll Up (↑/W)", True, self.gold_color)
                self.screen.blit(up_text, (scroll_rect.x + 20, scroll_rect.y + scroll_rect.height - 60))

            if scroll_offset < max_scroll:
                down_text = self.font_small.render("▼ Scroll Down (↓/S)", True, self.gold_color)
                self.screen.blit(down_text, (scroll_rect.x + 20, scroll_rect.y + scroll_rect.height - 40))

            # Draw continue instruction
            continue_text = self.font_small.render("Press SPACE or ENTER to continue...", True, self.gold_color)
            continue_rect = continue_text.get_rect(centerx=scroll_rect.centerx, y=scroll_rect.y + scroll_rect.height - 20)
            self.screen.blit(continue_text, continue_rect)

            pygame.display.flip()
            pygame.time.wait(50)

        return True

    def prepare_character_summary_content(self, character: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Prepare all character summary content for scrollable display"""
        content_lines = []

        # Helper function to add line
        def add_line(text: str, font: pygame.font.Font = None, color: tuple = None):
            if font is None:
                font = self.font_medium
            if color is None:
                color = self.ink_color
            content_lines.append({'text': text, 'font': font, 'color': color})

        # Basic Information
        add_line("═══ BASIC INFORMATION ═══", self.font_large, self.blood_red)
        add_line("")

        race = character['race']
        gender = character['gender']

        add_line(f"🧝 Race: {race['name']} ({race['rarity']}, {race['quality']})", self.font_medium, self.gold_color)
        add_line(f"⚧️ Gender: {gender['name']}", self.font_medium, self.gold_color)
        add_line("")

        # DNA Traits
        add_line("═══ DNA TRAITS ═══", self.font_large, self.blood_red)
        add_line("")

        for category, trait in character['dna_traits'].items():
            quality_indicator = ""
            if trait['quality'] == 'positive':
                quality_indicator = " 🔺"
            elif trait['quality'] == 'negative':
                quality_indicator = " 🔻"

            add_line(f"📌 {trait['rarity'].title()}{quality_indicator}", self.font_small, self.gold_color)
            add_line(f"{trait['category_icon']} {trait['category_name']}: {trait['name']}")

            # Show effects
            effects_text = []
            for stat, value in trait['effects'].items():
                if isinstance(value, (int, float)) and abs(value) < 1:
                    effects_text.append(f"{value:+.0%} {stat}")
                else:
                    effects_text.append(f"{value:+d} {stat}")

            if effects_text:
                add_line(f"   Effects: {', '.join(effects_text)}", self.font_small)

            # Show magic elements if present
            if trait.get('magic_elements'):
                from .dna_traits import MAGIC_ELEMENTS
                elements_display = []
                for element in trait['magic_elements']:
                    element_data = MAGIC_ELEMENTS[element]
                    elements_display.append(f"{element_data['icon']} {element_data['name']}")
                add_line(f"   🔮 Elemental Affinities: {', '.join(elements_display)}", self.font_small, self.gold_color)

            add_line("")

        # Final Stats
        add_line("═══ FINAL STATISTICS ═══", self.font_large, self.blood_red)
        add_line("")

        stats = character['effective_stats']

        # Core stats
        add_line("Core Stats:", self.font_medium, self.gold_color)
        for stat in ['STR', 'DEX', 'VIT', 'INT', 'SPE', 'WILL']:
            if stat in stats:
                add_line(f"   {stat}: {stats[stat]}")
        add_line("")

        # Special stats
        add_line("Special Stats:", self.font_medium, self.gold_color)
        special_stats = ['magic_affinity', 'beauty', 'estimated_lifespan', 'height']
        for stat in special_stats:
            if stat in stats:
                if stat == 'height':
                    add_line(f"   Height: {stats[stat]} cm")
                elif stat == 'estimated_lifespan':
                    add_line(f"   Lifespan: {stats[stat]} years")
                else:
                    add_line(f"   {stat.replace('_', ' ').title()}: {stats[stat]}")
        add_line("")

        # Elemental bonuses
        elemental_stats = [stat for stat in stats.keys() if 'damage_bonus' in stat or 'resist_bonus' in stat]
        if elemental_stats:
            add_line("Elemental Bonuses:", self.font_medium, self.gold_color)
            for stat in sorted(elemental_stats):
                if stats[stat] > 0:
                    stat_display = stat.replace('_', ' ').title()
                    add_line(f"   {stat_display}: +{stats[stat]}")
            add_line("")

        # Background History
        if character.get('background_history'):
            add_line("═══ BACKGROUND HISTORY ═══", self.font_large, self.blood_red)
            add_line("")

            current_stage = ""
            for event in character['background_history']:
                if event['life_stage'] != current_stage:
                    current_stage = event['life_stage']
                    add_line(f"🔸 {current_stage.upper()}", self.font_medium, self.gold_color)
                    add_line("")

                age_text = f"Age {event['age']}: {event['event']}"
                add_line(age_text)

                if event.get('effects') and event['type'] != 'growth':
                    effects = ", ".join([f"{v:+d} {k}" for k, v in event['effects'].items()])
                    add_line(f"   Effects: {effects}", self.font_small, self.gold_color)

                if event.get('traits_gained'):
                    traits = ", ".join(event['traits_gained'])
                    add_line(f"   Traits: {traits}", self.font_small, self.blood_red)

                if event.get('skills_gained'):
                    skills = ", ".join([f"{s['name']} (Lv{s.get('level', 1)})" for s in event['skills_gained']])
                    add_line(f"   Skills: {skills}", self.font_small, self.gold_color)

                add_line("")

        return content_lines

    def run_character_generation(self) -> Optional[Dict[str, Any]]:
        """Run the complete character generation process - character sheet only shown at the end"""
        try:
            # Introduction
            if not self.display_dramatic_pause(3.0, "🌟 Beginning your one and only life..."):
                return None

            if not self.display_dramatic_pause(2.0, "⚠️ Remember: There are no second chances"):
                return None

            # Generate race
            race_data = self.generator.generate_race()
            if not self.display_race_reveal(race_data):
                return None

            # Generate gender
            gender_data = self.generator.generate_gender()
            if not self.display_gender_reveal(gender_data):
                return None

            # Generate DNA traits
            if not self.display_dramatic_pause(2.0, "🧬 Analyzing genetic heritage..."):
                return None

            dna_traits = self.generator.generate_all_dna_traits()
            for trait_data in dna_traits.values():
                if not self.display_dna_trait_reveal(trait_data):
                    return None

            # Generate name and calculate final stats (no display yet)
            self.generator._generate_character_name()
            self.generator.calculate_final_stats()

            # Background history generation
            if not self.display_dramatic_pause(2.0, "📜 Weaving your life's tapestry..."):
                return None

            # Generate background history
            from .background_history_generator import BackgroundHistoryGenerator
            history_gen = BackgroundHistoryGenerator(self.generator.character_profile)
            background_history = history_gen.generate_full_background()
            updated_character = history_gen.get_updated_character()

            # Display background history
            if not self.display_background_history(background_history):
                return None

            # Display final character summary
            if not self.display_character_summary(updated_character):
                return None

            # Final completion
            if not self.display_dramatic_pause(3.0, "🎭 Your destiny awaits..."):
                return None

            # NOW show the character sheet for the first time
            print("\n🎭 Character generation complete!")
            print("Your fate has been sealed...")

            return updated_character

        except Exception as e:
            print(f"Error during character generation: {e}")
            return None
