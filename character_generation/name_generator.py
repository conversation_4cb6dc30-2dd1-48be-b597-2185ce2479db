"""
Name Generator for One Life Isekai
Generates fantasy names based on race and gender
"""

import random
from typing import Dict, List


class NameGenerator:
    def __init__(self):
        self.name_data = {
            'human': {
                'male': {
                    'first': ['<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
                             '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>'],
                    'last': ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>',
                            '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>']
                },
                'female': {
                    'first': ['<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>',
                             '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
                    'last': ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>',
                            '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>']
                }
            },
            'el<PERSON>': {
                'male': {
                    'first': ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>nn<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
                             '<PERSON><PERSON>', 'Nutae', 'Paelynn', 'Peren', 'Quarion', 'Riardon', 'Silvyr', 'Suhnaal', 'Thamior', 'Theriatis', 'Thervan', 'Uthemar'],
                    'last': ['Amakir', 'Amarthen', 'Amarillis', 'Helder', 'Hornraven', 'Heringstar', 'Liadon', 'Meliamne',
                            'Nailo', 'Siannodel', 'Xiloscient', 'Alderleaf', 'Brushgather', 'Goodbarrel', 'Greenbottle', 'High-hill']
                },
                'female': {
                    'first': ['Adrie', 'Althaea', 'Anastrianna', 'Andraste', 'Antinua', 'Bethrynna', 'Birel', 'Caelynn', 'Dara', 'Enna',
                             'Galinndan', 'Hadarai', 'Halimath', 'Heian', 'Himo', 'Immeral', 'Ivellios', 'Korfel', 'Lamlis', 'Laucian'],
                    'last': ['Amakir', 'Amarthen', 'Amarillis', 'Helder', 'Hornraven', 'Heringstar', 'Liadon', 'Meliamne',
                            'Nailo', 'Siannodel', 'Xiloscient', 'Alderleaf', 'Brushgather', 'Goodbarrel', 'Greenbottle', 'High-hill']
                }
            },
            'dwarf': {
                'male': {
                    'first': ['Adrik', 'Baern', 'Darrak', 'Eberk', 'Fargrim', 'Gardain', 'Harbek', 'Kildrak', 'Morgran', 'Orsik',
                             'Rangrim', 'Taklinn', 'Thorek', 'Thorin', 'Tordek', 'Traubon', 'Ulfgar', 'Veit', 'Vonbin', 'Baradad'],
                    'last': ['Battlehammer', 'Brawnanvil', 'Dankil', 'Fireforge', 'Frostbeard', 'Gorunn', 'Holderhek', 'Ironfist',
                            'Loderr', 'Lutgehr', 'Rumnaheim', 'Strakeln', 'Torunn', 'Ungart', 'Axebreaker', 'Battlehammer']
                },
                'female': {
                    'first': ['Amber', 'Bardryn', 'Diesa', 'Eldeth', 'Gunnloda', 'Gwyn', 'Kathra', 'Kilia', 'Mardred', 'Riswynn',
                             'Sannl', 'Torbera', 'Torgga', 'Vistra', 'Ilde', 'Liftrasa', 'Mardred', 'Riswynn', 'Sannl', 'Torbera'],
                    'last': ['Battlehammer', 'Brawnanvil', 'Dankil', 'Fireforge', 'Frostbeard', 'Gorunn', 'Holderhek', 'Ironfist',
                            'Loderr', 'Lutgehr', 'Rumnaheim', 'Strakeln', 'Torunn', 'Ungart', 'Axebreaker', 'Battlehammer']
                }
            },
            'halfling': {
                'male': {
                    'first': ['Alton', 'Ander', 'Bernie', 'Bobbin', 'Cade', 'Callus', 'Corrin', 'Dannad', 'Garret', 'Lindal',
                             'Lyle', 'Merric', 'Milo', 'Osborn', 'Perrin', 'Reed', 'Roscoe', 'Wellby', 'Wendel', 'Finnan'],
                    'last': ['Brushgather', 'Goodbarrel', 'Greenbottle', 'High-hill', 'Hilltopple', 'Leagallow', 'Tealeaf', 'Thorngage',
                            'Tosscobble', 'Underbough', 'Axebreaker', 'Battlehammer', 'Brawnanvil', 'Dankil', 'Fireforge', 'Frostbeard']
                },
                'female': {
                    'first': ['Andry', 'Bree', 'Callie', 'Cora', 'Euphemia', 'Jillian', 'Kithri', 'Lavinia', 'Lidda', 'Merla',
                             'Nedda', 'Paela', 'Portia', 'Seraphina', 'Shaena', 'Trym', 'Vani', 'Verna', 'Kithri', 'Lavinia'],
                    'last': ['Brushgather', 'Goodbarrel', 'Greenbottle', 'High-hill', 'Hilltopple', 'Leagallow', 'Tealeaf', 'Thorngage',
                            'Tosscobble', 'Underbough', 'Axebreaker', 'Battlehammer', 'Brawnanvil', 'Dankil', 'Fireforge', 'Frostbeard']
                }
            },
            'dragonborn': {
                'male': {
                    'first': ['Arjhan', 'Balasar', 'Bharash', 'Donaar', 'Ghesh', 'Heskan', 'Kriv', 'Medrash', 'Mehen', 'Nadarr',
                             'Pandjed', 'Patrin', 'Rhogar', 'Shamash', 'Shedinn', 'Tarhun', 'Torinn', 'Akra', 'Aasathra', 'Antrara'],
                    'last': ['Clethtinthiallor', 'Daardendrian', 'Delmirev', 'Drachedandion', 'Fenkenkabradon', 'Kepeshkmolik', 'Kerrhylon',
                            'Kimbatuul', 'Linxakasendalor', 'Myastan', 'Nemmonis', 'Norixius', 'Ophinshtalajiir', 'Prexijandilin', 'Shestendeliath', 'Turnuroth']
                },
                'female': {
                    'first': ['Akra', 'Aasathra', 'Antrara', 'Arava', 'Biri', 'Blendaeth', 'Burana', 'Chassath', 'Daar', 'Dentratha',
                             'Doudra', 'Driindar', 'Eggren', 'Farideh', 'Findex', 'Furrele', 'Harann', 'Havilar', 'Hekstra', 'Hisora'],
                    'last': ['Clethtinthiallor', 'Daardendrian', 'Delmirev', 'Drachedandion', 'Fenkenkabradon', 'Kepeshkmolik', 'Kerrhylon',
                            'Kimbatuul', 'Linxakasendalor', 'Myastan', 'Nemmonis', 'Norixius', 'Ophinshtalajiir', 'Prexijandilin', 'Shestendeliath', 'Turnuroth']
                }
            }
        }

    def generate_name(self, race: str, gender: str) -> str:
        """Generate a name based on race and gender"""
        # Normalize inputs
        race = race.lower()
        gender = gender.lower()

        # Handle intersex as random choice between male/female names
        if gender == 'intersex':
            gender = random.choice(['male', 'female'])

        # Get name data for race and gender
        if race in self.name_data and gender in self.name_data[race]:
            first_names = self.name_data[race][gender]['first']
            last_names = self.name_data[race][gender]['last']

            first_name = random.choice(first_names)
            last_name = random.choice(last_names)

            return f"{first_name} {last_name}"

        # Fallback to human names if race not found
        if gender in self.name_data['human']:
            first_names = self.name_data['human'][gender]['first']
            last_names = self.name_data['human'][gender]['last']

            first_name = random.choice(first_names)
            last_name = random.choice(last_names)

            return f"{first_name} {last_name}"

        # Ultimate fallback
        return "Unknown Wanderer"

    def generate_random_name(self) -> str:
        """Generate a completely random fantasy name"""
        race = random.choice(list(self.name_data.keys()))
        gender = random.choice(['male', 'female'])
        return self.generate_name(race, gender)
