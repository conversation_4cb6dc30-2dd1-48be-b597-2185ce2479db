"""
AI-driven character generator for One Life Isekai
Phase 1: Race, Gender, and DNA trait generation
"""

import random
import time
from typing import Dict, Any, Tu<PERSON>
from .races import RACES, RACE_RARITY_WEIGHTS, RACE_QUALITY_DESCRIPTORS
from .dna_traits import DNA_TRAIT_CATEGORIES, GENDER_OPTIONS, RARITY_DESCRIPTORS, QUALITY_DESCRIPTORS, MAGIC_ELEMENTS
from .stat_definitions import ALL_STATS


class CharacterGenerator:
    def __init__(self, logger=None):
        self.character_profile = {
            'race': {},
            'gender': {},
            'dna_traits': {},
            'base_stats': {},
            'effective_stats': {},
            'traits': [],
            'skills': [],
            'generation_log': []
        }
        self.logger = logger
    
    def weighted_random_choice(self, choices: Dict[str, Any], weight_key: str = 'weight') -> str:
        """Select random choice based on weights"""
        total_weight = sum(choice.get(weight_key, 1) for choice in choices.values())
        random_value = random.uniform(0, total_weight)
        
        current_weight = 0
        for key, choice in choices.items():
            current_weight += choice.get(weight_key, 1)
            if random_value <= current_weight:
                return key
        
        return list(choices.keys())[-1]
    
    def generate_race(self) -> Dict[str, Any]:
        """Generate character race with rarity-weighted selection"""
        # Create weighted race pool
        race_pool = {}
        for race_id, race_data in RACES.items():
            weight = RACE_RARITY_WEIGHTS[race_data['rarity']]
            race_pool[race_id] = {'weight': weight, **race_data}
        
        # Select race
        selected_race_id = self.weighted_random_choice(race_pool)
        selected_race = RACES[selected_race_id]
        
        # Generate base stats within race ranges
        base_stats = {}
        for stat, (min_val, max_val) in selected_race['base_stats'].items():
            base_stats[stat] = random.randint(min_val, max_val)
        
        # Generate utility stats
        utility_stats = {}
        for stat, value in selected_race['utility_stats'].items():
            if isinstance(value, tuple):
                utility_stats[stat] = random.randint(value[0], value[1])
            else:
                utility_stats[stat] = value
        
        race_result = {
            'id': selected_race_id,
            'name': selected_race['name'],
            'rarity': selected_race['rarity'],
            'quality': selected_race['quality'],
            'base_stats': base_stats,
            'utility_stats': utility_stats,
            'innate_traits': selected_race['innate_traits'].copy(),
            'bonus_chances': selected_race['bonus_chances'].copy(),
            'description': selected_race['description']
        }
        
        self.character_profile['race'] = race_result
        self.character_profile['base_stats'].update(base_stats)
        self.character_profile['effective_stats'].update(base_stats)
        self.character_profile['effective_stats'].update(utility_stats)
        
        self.log_generation_step('race', f"Generated {selected_race['name']} ({selected_race['rarity']}, {selected_race['quality']})")
        
        return race_result
    
    def generate_gender(self) -> Dict[str, Any]:
        """Generate character gender with weighted selection"""
        selected_gender_id = self.weighted_random_choice(GENDER_OPTIONS)
        selected_gender = GENDER_OPTIONS[selected_gender_id]
        
        gender_result = {
            'id': selected_gender_id,
            'name': selected_gender['name'],
            'icon': selected_gender['icon'],
            'effects': selected_gender['effects'].copy()
        }
        
        # Apply gender effects
        self.apply_stat_effects(selected_gender['effects'])
        
        self.character_profile['gender'] = gender_result
        self.log_generation_step('gender', f"Generated {selected_gender['name']}")
        
        return gender_result
    
    def generate_dna_trait(self, category_id: str) -> Dict[str, Any]:
        """Generate a single DNA trait from specified category"""
        category = DNA_TRAIT_CATEGORIES[category_id]

        # Select trait based on weights
        selected_trait_id = self.weighted_random_choice(category['traits'])
        selected_trait = category['traits'][selected_trait_id]

        # Generate specific values for range-based effects
        effects = {}
        for stat, value in selected_trait['effects'].items():
            if isinstance(value, tuple):
                effects[stat] = random.randint(value[0], value[1])
            else:
                effects[stat] = value

        # Handle magic elements for magic_affinity category
        magic_elements = []
        if category_id == 'magic_affinity' and 'elements' in selected_trait:
            element_count = selected_trait['elements']
            # Handle both list and integer cases
            if isinstance(element_count, list):
                magic_elements = element_count
            elif isinstance(element_count, int) and element_count > 0:
                # Select random elements
                available_elements = list(MAGIC_ELEMENTS.keys())
                magic_elements = random.sample(available_elements, min(element_count, len(available_elements)))

            # Add elemental bonuses to effects
            if magic_elements:
                for element in magic_elements:
                    element_data = MAGIC_ELEMENTS[element]
                    # Base damage and resist bonuses based on magic affinity level
                    base_bonus = max(5, effects.get('magic_affinity', 70) // 10)
                    effects[element_data['damage_stat']] = base_bonus
                    effects[element_data['resist_stat']] = base_bonus

        trait_result = {
            'category': category_id,
            'category_name': category['name'],
            'category_icon': category['icon'],
            'id': selected_trait_id,
            'name': selected_trait['name'],
            'rarity': selected_trait['rarity'],
            'quality': selected_trait['quality'],
            'effects': effects,
            'magic_elements': magic_elements  # Store selected elements
        }

        # Apply trait effects
        self.apply_stat_effects(effects)

        self.character_profile['dna_traits'][category_id] = trait_result
        self.log_generation_step('dna_trait', f"Generated {category['name']}: {selected_trait['name']} ({selected_trait['rarity']}, {selected_trait['quality']})")

        return trait_result
    
    def generate_all_dna_traits(self) -> Dict[str, Any]:
        """Generate all DNA trait categories"""
        dna_results = {}
        
        for category_id in DNA_TRAIT_CATEGORIES.keys():
            trait_result = self.generate_dna_trait(category_id)
            dna_results[category_id] = trait_result
        
        return dna_results
    
    def apply_stat_effects(self, effects: Dict[str, Any]) -> None:
        """Apply stat effects to character profile"""
        for stat, value in effects.items():
            if stat in self.character_profile['effective_stats']:
                if isinstance(value, (int, float)) and value > 0 and value < 1:
                    # Percentage modifier
                    self.character_profile['effective_stats'][stat] *= (1 + value / 100)
                else:
                    # Flat modifier
                    self.character_profile['effective_stats'][stat] += value
            else:
                # New stat
                self.character_profile['effective_stats'][stat] = value
    
    def log_generation_step(self, step_type: str, description: str) -> None:
        """Log generation step for debugging and tracking"""
        log_entry = {
            'step': step_type,
            'description': description,
            'timestamp': time.time()
        }
        self.character_profile['generation_log'].append(log_entry)
    
    def display_race_reveal(self, race_data: Dict[str, Any]) -> None:
        """Display dramatic race reveal"""
        print("\n" + "═" * 50)
        print("🔮 Origin Revealed...")
        print("─" * 30)
        
        rarity_text = RARITY_DESCRIPTORS[race_data['rarity']]
        quality_text = RACE_QUALITY_DESCRIPTORS[race_data['quality']]
        
        print(f"🔸 Rarity: {rarity_text} ({quality_text})")
        print(f"🧝 Race: {race_data['name']}")
        print("─" * 30)
        
        # Display stat ranges
        for stat, value in race_data['base_stats'].items():
            stat_info = ALL_STATS.get(stat, {})
            stat_name = stat_info.get('name', stat)
            print(f"{stat}: {value} ({self.evaluate_stat_quality(stat, value)})")
        
        print("─" * 30)
        
        # Display utility stats
        for stat, value in race_data['utility_stats'].items():
            if stat == 'height':
                print(f"Height: {value} cm")
            elif stat == 'beauty':
                print(f"Beauty: {value}")
            elif stat == 'estimated_lifespan':
                print(f"Lifespan: {value} years")
            elif stat == 'load_capacity_bonus':
                if value != 0:
                    print(f"Load Capacity Bonus: {value:+d} kg")
        
        print("─" * 30)
        
        # Display innate traits
        if race_data['innate_traits']:
            print(f"Innate Traits: {', '.join(race_data['innate_traits'])}")
        
        # Display bonus chances
        for bonus, chance in race_data['bonus_chances'].items():
            bonus_name = bonus.replace('_', ' ').title()
            print(f"Bonus: {chance}% Chance {bonus_name}")
        
        print("═" * 50)
    
    def display_gender_reveal(self, gender_data: Dict[str, Any]) -> None:
        """Display dramatic gender reveal"""
        print("\n" + "═" * 40)
        print("Your physical identity is...")
        print(f"{gender_data['icon']} {gender_data['name']}")
        
        # Display bonuses
        effects_text = []
        for stat, value in gender_data['effects'].items():
            if isinstance(value, (int, float)) and value > 0 and value < 1:
                effects_text.append(f"+{value}% {stat.title()}")
            else:
                effects_text.append(f"+{value} {stat.title()}")
        
        if effects_text:
            print(f"Bonuses applied: {', '.join(effects_text)}")
        
        print("═" * 40)
    
    def display_dna_trait_reveal(self, trait_data: Dict[str, Any]) -> None:
        """Display dramatic DNA trait reveal"""
        print("\n" + "─" * 40)

        # Build display text
        rarity_text = RARITY_DESCRIPTORS[trait_data['rarity']]
        quality_text = QUALITY_DESCRIPTORS[trait_data['quality']]

        # Format effects
        effects_text = []
        for stat, value in trait_data['effects'].items():
            if isinstance(value, (int, float)) and abs(value) < 1:
                effects_text.append(f"{value:+.0%} {stat.title()}")
            else:
                effects_text.append(f"{value:+d} {stat.title()}")

        effects_str = ", ".join(effects_text) if effects_text else "No effects"

        # Display in requested format
        header = f"{trait_data['category_icon']} {trait_data['category_name']}: "

        if quality_text:
            print(f"📌 {rarity_text} {quality_text}")
            print(f"{header}{trait_data['name']} ({effects_str})")
        else:
            print(f"📌 {rarity_text}")
            print(f"{header}{trait_data['name']} ({effects_str})")

        # Display magic elements if present
        if trait_data.get('magic_elements'):
            elements_display = []
            for element in trait_data['magic_elements']:
                element_data = MAGIC_ELEMENTS[element]
                elements_display.append(f"{element_data['icon']} {element_data['name']}")
            print(f"🔮 Elemental Affinities: {', '.join(elements_display)}")

        print("─" * 40)
    
    def evaluate_stat_quality(self, stat: str, value: int) -> str:
        """Evaluate if a stat value is positive, average, or negative"""
        # This is a simplified evaluation - could be enhanced with race-specific logic
        if value >= 50:
            return "Positive"
        elif value >= 30:
            return "Average"
        else:
            return "Negative"
    
    def generate_full_character(self, display: bool = True) -> Dict[str, Any]:
        """Generate complete character through all phases"""
        print("🌟 Beginning character generation...")
        print("⚠️  Remember: This is your one and only life. No rerolls.")
        
        time.sleep(1)
        
        # Phase 1: Race Generation
        race_data = self.generate_race()
        if display:
            self.display_race_reveal(race_data)
            time.sleep(2)
        
        # Phase 2: Gender Generation
        gender_data = self.generate_gender()
        if display:
            self.display_gender_reveal(gender_data)
            time.sleep(2)
        
        # Phase 3: DNA Trait Generation
        print("\n🧬 Analyzing genetic code...")
        time.sleep(1)
        
        dna_traits = self.generate_all_dna_traits()
        if display:
            for trait_data in dna_traits.values():
                self.display_dna_trait_reveal(trait_data)
                time.sleep(1)
        
        # Phase 4: Generate name
        self._generate_character_name()

        # Phase 5: Calculate final stats
        self.calculate_final_stats()

        print("\n🎭 Character generation complete!")
        print("Your fate has been sealed...")

        return self.character_profile

    def _generate_character_name(self) -> None:
        """Generate character name based on race and gender"""
        from .name_generator import NameGenerator

        name_gen = NameGenerator()
        race_name = self.character_profile['race']['name']
        gender_name = self.character_profile['gender']['name']

        character_name = name_gen.generate_name(race_name, gender_name)
        self.character_profile['name'] = character_name

        self.log_generation_step('name', f"Generated name: {character_name}")

    def calculate_final_stats(self) -> Dict[str, Any]:
        """Calculate final effective stats after all modifiers"""
        final_stats = self.character_profile['base_stats'].copy()

        # Apply race utility stats first
        race_data = self.character_profile.get('race', {})
        if 'utility_stats' in race_data:
            for stat, value in race_data['utility_stats'].items():
                final_stats[stat] = final_stats.get(stat, 0) + value

        # Apply race bonuses
        if 'base_stats' in race_data:
            for stat, value in race_data['base_stats'].items():
                final_stats[stat] = final_stats.get(stat, 0) + value

        # Apply gender effects
        gender_data = self.character_profile.get('gender', {})
        if 'effects' in gender_data:
            for stat, value in gender_data['effects'].items():
                final_stats[stat] = final_stats.get(stat, 0) + value

        # Apply DNA trait effects
        dna_traits = self.character_profile.get('dna_traits', {})
        for trait_category, trait_data in dna_traits.items():
            if 'effects' in trait_data:
                for stat, value in trait_data['effects'].items():
                    final_stats[stat] = final_stats.get(stat, 0) + value

        # Calculate derived stats
        self._calculate_derived_stats(final_stats)

        # Store final stats
        self.character_profile['effective_stats'] = final_stats

        return final_stats

    def _calculate_derived_stats(self, stats: Dict[str, Any]) -> None:
        """Calculate derived stats based on base stats"""
        # Calculate HP from STR and VIT
        str_val = stats.get('STR', 50)
        vit_val = stats.get('VIT', 50)
        stats['hp'] = int((str_val * 0.5) + (vit_val * 1.0) + 50)

        # Calculate load capacity from STR and height
        height = stats.get('height', 170)
        load_capacity_bonus = stats.get('load_capacity_bonus', 0)
        base_load = (str_val * 1.0) + (height * 0.1)
        stats['load_capacity'] = int(base_load + load_capacity_bonus)

        # Calculate BMI from weight and height
        weight = stats.get('weight', 70)
        if height > 0:
            height_m = height / 100.0
            stats['bmi'] = round(weight / (height_m * height_m), 1)
        else:
            stats['bmi'] = 20.0

        # Calculate daily calorie requirement
        # Basic formula: BMR + activity factor
        gender_name = self.character_profile.get('gender', {}).get('name', 'Male')
        if gender_name == 'Male':
            bmr = 88.362 + (13.397 * weight) + (4.799 * height) - (5.677 * 25)  # Assume age 25
        else:
            bmr = 447.593 + (9.247 * weight) + (3.098 * height) - (4.330 * 25)  # Assume age 25

        # Activity factor based on STR and DEX
        activity_factor = 1.2 + (str_val + stats.get('DEX', 50)) / 1000
        stats['daily_calorie_requirement'] = int(bmr * activity_factor)

        # Calculate magic damage from INT and magic affinity
        int_val = stats.get('INT', 50)
        magic_affinity = stats.get('magic_affinity', 0)
        stats['magic_damage'] = int((int_val * 1.0) + (magic_affinity * 0.5))

        # Calculate melee damage from STR and DEX
        dex_val = stats.get('DEX', 50)
        stats['melee_damage'] = int((str_val * 1.0) + (dex_val * 0.33))

        # Calculate ranged damage from DEX and STR
        stats['ranged_damage'] = int((dex_val * 1.0) + (str_val * 0.33))

        # Calculate critical hit chance from DEX
        stats['critical_hit_chance'] = min(50, int(dex_val * 0.5))  # Cap at 50%

        # Calculate hit chance from DEX and perception
        perception = stats.get('perception', 50)
        stats['hit_chance'] = min(95, int(50 + (dex_val * 0.3) + (perception * 0.2)))  # Cap at 95%

        # Calculate travel speed from SPE
        spe_val = stats.get('SPE', 50)
        stats['travel_speed'] = int(spe_val * 1.0)

        # Calculate magic cast speed from INT and WILL
        will_val = stats.get('WILL', 50)
        stats['magic_cast_speed'] = int((int_val * 0.5) + (will_val * 0.5))

        # Set default weight if not set
        if 'weight' not in stats:
            # Calculate weight based on height and build (STR/VIT)
            build_factor = (str_val + vit_val) / 100.0
            base_weight = (height - 100) * 0.9  # Basic weight formula
            stats['weight'] = int(base_weight * build_factor)

            # Recalculate BMI with new weight
            if height > 0:
                height_m = height / 100.0
                stats['bmi'] = round(stats['weight'] / (height_m * height_m), 1)
