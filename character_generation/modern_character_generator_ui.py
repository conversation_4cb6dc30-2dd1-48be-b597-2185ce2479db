"""
Modern Character Generator UI for One Life Isekai
Features MMO-style character sheet with animated reveals
"""

import pygame
import time
import math
import os
from typing import Dict, Any, Optional
from .character_generator import CharacterGenerator
from .character_logger import CharacterLogger
from .name_generator import NameGenerator
from .modern_character_sheet import ModernCharacterSheet
from utils.constants import COLOR<PERSON>, SCREEN_WIDTH, SCREEN_HEIGHT


class ModernCharacterGeneratorUI:
    def __init__(self, screen: pygame.Surface):
        self.screen = screen
        self.generator = CharacterGenerator()
        self.logger = CharacterLogger()
        self.name_generator = NameGenerator()
        
        # Load fonts
        self._load_fonts()
        
        # Create character sheet
        self.character_sheet = ModernCharacterSheet(screen, self.fonts)
        
        # Animation state
        self.current_step = 0
        self.reveal_progress = 0.0
        self.popup_alpha = 0
        self.popup_text = ""
        self.popup_subtext = ""
        self.popup_color = (255, 255, 255)

        # Sound system
        self._init_sounds()
        
        # Colors
        self.bg_color = (15, 15, 25)
        self.popup_bg = (40, 40, 60)
        self.gold_color = (255, 215, 0)
        self.green_color = (50, 205, 50)
        self.red_color = (220, 20, 60)
        self.white_color = (255, 255, 255)
        
        # Character data
        self.character_data = None
    
    def _load_fonts(self):
        """Load fonts for the UI"""
        try:
            self.fonts = {
                'large': pygame.font.Font(None, 32),      # Smaller header text
                'medium': pygame.font.Font(None, 24),     # Smaller section titles
                'small': pygame.font.Font(None, 18),      # Smaller stat text
                'tiny': pygame.font.Font(None, 16),       # Very small text
                'popup_large': pygame.font.Font(None, 48), # Popup titles
                'popup_medium': pygame.font.Font(None, 32) # Popup text
            }
        except:
            # Fallback fonts
            self.fonts = {
                'large': pygame.font.Font(None, 32),
                'medium': pygame.font.Font(None, 24),
                'small': pygame.font.Font(None, 18),
                'tiny': pygame.font.Font(None, 16),
                'popup_large': pygame.font.Font(None, 48),
                'popup_medium': pygame.font.Font(None, 32)
            }

    def _init_sounds(self):
        """Initialize sound system"""
        try:
            pygame.mixer.init()
            self.sounds = {
                'reveal': None,  # Will be loaded if available
                'rare': None,
                'epic': None,
                'legendary': None,
                'positive': None,
                'negative': None,
                'click': None
            }
            # For now, sounds are disabled - can be added later
            self.sounds_enabled = False
        except:
            self.sounds_enabled = False
            self.sounds = {}

    def _play_sound(self, sound_name: str):
        """Play a sound if available"""
        if self.sounds_enabled and sound_name in self.sounds and self.sounds[sound_name]:
            try:
                self.sounds[sound_name].play()
            except:
                pass

    def load_race_image(self, race_name: str) -> pygame.Surface:
        """Load race image"""
        # Convert race name to filename format
        race_filename = race_name.lower().replace('-', '-').replace(' ', '-')

        # Try to load race image
        image_path = f"pictures/race/{race_filename}.png"
        if os.path.exists(image_path):
            try:
                image = pygame.image.load(image_path)
                return pygame.transform.scale(image, (300, 300))
            except:
                pass

        # Use unknown placeholder
        unknown_path = "pictures/race/unknown.png"
        if os.path.exists(unknown_path):
            try:
                image = pygame.image.load(unknown_path)
                return pygame.transform.scale(image, (300, 300))
            except:
                pass

        # Create placeholder
        placeholder = pygame.Surface((300, 300))
        placeholder.fill((60, 60, 80))
        return placeholder

    def load_gender_image(self, gender_name: str) -> pygame.Surface:
        """Load gender image"""
        # Use the correct gender images from pictures/gender/
        image_path = f"pictures/gender/{gender_name.lower()}.png"
        if os.path.exists(image_path):
            try:
                image = pygame.image.load(image_path)
                return pygame.transform.scale(image, (100, 100))
            except:
                pass

        # Use unknown placeholder
        unknown_path = "pictures/gender/unknown.png"
        if os.path.exists(unknown_path):
            try:
                image = pygame.image.load(unknown_path)
                return pygame.transform.scale(image, (100, 100))
            except:
                pass

        # Create placeholder
        placeholder = pygame.Surface((100, 100))
        placeholder.fill((60, 60, 80))
        return placeholder
    
    def run_character_generation(self) -> Optional[Dict[str, Any]]:
        """Run the complete modern character generation process"""
        try:
            # Start logging
            self.logger.start_generation()

            # Show initial loading screen
            if not self._show_generation_loading():
                return None

            # Generate complete character first
            if not self._generate_complete_character():
                return None

            # Initialize character sheet with generated data
            self.character_sheet.initialize_character_sheet(self.character_data)
            self.character_sheet.character_data = self.character_data
            self.character_sheet.update_readable_stats(self.character_data)

            # Show character sheet with unreadable text
            self._display_initial_sheet()

            # Now reveal each part step by step
            if not self._reveal_race():
                return None

            if not self._reveal_gender():
                return None

            if not self._reveal_name():
                return None

            if not self._reveal_dna_traits():
                return None

            if not self._reveal_background_history():
                return None

            # Final reveal and summary
            if not self._final_character_reveal():
                return None

            # Save character
            if not self._save_character():
                return None

            # Finalize logging
            self.logger.finalize_log(self.character_data)

            return self.character_data

        except Exception as e:
            print(f"Error during character generation: {e}")
            return None
    
    def _display_initial_sheet(self):
        """Display the initial character sheet with unreadable text"""
        for _ in range(60):  # Show for 1 second
            self.screen.fill(self.bg_color)
            self.character_sheet.draw_character_sheet(0.0)
            
            # Draw title
            title_text = self.fonts['popup_large'].render("🌟 DESTINY AWAKENS 🌟", True, self.gold_color)
            title_rect = title_text.get_rect(center=(SCREEN_WIDTH // 2, 100))
            self.screen.blit(title_text, title_rect)
            
            pygame.display.flip()
            pygame.time.wait(16)  # ~60 FPS
            
            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return False
        
        return True

    def _show_generation_loading(self) -> bool:
        """Show loading screen while generating character"""
        if not self._show_step_popup("🌟 Weaving Your Destiny... 🌟", self.gold_color, auto_advance=True, duration=3.0):
            return False
        return True

    def _generate_complete_character(self) -> bool:
        """Generate the complete character before showing anything"""
        try:
            # Generate race
            race_data = self.generator.generate_race()
            self.logger.log_race_generation(race_data)

            # Generate gender
            gender_data = self.generator.generate_gender()
            self.logger.log_gender_generation(gender_data)

            # Generate name
            race_name = self.generator.character_profile['race']['name'].lower()
            gender_name = self.generator.character_profile['gender']['name'].lower()
            character_name = self.name_generator.generate_name(race_name, gender_name)
            self.generator.character_profile['name'] = character_name
            self.logger.log_name_generation(character_name)

            # Generate DNA traits
            dna_traits = self.generator.generate_all_dna_traits()
            for trait_data in dna_traits.values():
                self.logger.log_dna_trait_generation(trait_data)

            # Generate background history
            try:
                from .background_history_generator import BackgroundHistoryGenerator
                history_gen = BackgroundHistoryGenerator(self.generator.character_profile)
                background_history = history_gen.generate_full_background()
                self.generator.character_profile = history_gen.get_updated_character()
                self.generator.character_profile['background_history'] = background_history
            except Exception as e:
                print(f"Background generation error: {e}")
                self.generator.character_profile['background_history'] = []

            # Store complete character data
            self.character_data = self.generator.character_profile

            return True

        except Exception as e:
            print(f"Error generating character: {e}")
            return False

    def _reveal_race(self) -> bool:
        """Reveal race information"""
        race_data = self.character_data['race']

        # Load race image
        race_image = self.load_race_image(race_data['name'])

        # Format effects for display
        effects = []
        for stat, value in race_data.get('base_stats', {}).items():
            if value != 0:
                effects.append(f"{value:+d} {stat}")
        for stat, value in race_data.get('utility_stats', {}).items():
            if value != 0:
                effects.append(f"{value:+d} {stat}")

        # Add "no bonus" if no effects
        if not effects:
            effects = ["no bonus"]

        # Determine colors - make rarity and quality uppercase
        rarity_text = race_data['rarity'].upper()
        quality_text = race_data['quality'].upper()

        if race_data['quality'] == 'positive':
            quality_color = self.green_color
        elif race_data['quality'] == 'negative':
            quality_color = self.red_color
        else:
            quality_color = self.white_color

        # Show single page popup with all info and image
        if not self._show_generation_result_popup_with_image("Race", rarity_text, quality_text,
                                                           race_data['name'], effects,
                                                           self.gold_color, quality_color, race_image):
            return False

        # Show race description with character-by-character typing
        race_description = race_data.get('description', 'asdasd')
        if not self._show_character_description(race_description, race_image):
            return False

        # Reveal character sheet lines
        self._reveal_character_lines(0, 5)
        return True

    def _reveal_gender(self) -> bool:
        """Reveal gender information"""
        gender_data = self.character_data['gender']

        # Load gender image
        gender_image = self.load_gender_image(gender_data['name'])

        # Format gender effects
        effects = []
        for stat, value in gender_data.get('effects', {}).items():
            if isinstance(value, (int, float)) and value != 0:
                if abs(value) < 1:
                    effects.append(f"{value:+.0%} {stat}")
                else:
                    effects.append(f"{value:+d} {stat}")

        # Add "no bonus" if no effects
        if not effects:
            effects = ["no bonus"]

        # Show popup with image - make text uppercase
        if not self._show_generation_result_popup_with_image("Gender", "DETERMINED", "AVERAGE",
                                                           f"{gender_data['icon']} {gender_data['name']}",
                                                           effects, self.gold_color, self.white_color, gender_image):
            return False

        self._reveal_character_lines(5, 7)
        return True

    def _reveal_name(self) -> bool:
        """Reveal character name"""
        character_name = self.character_data.get('name', 'NO NAME')

        if not self._show_generation_result_popup("Name", "REVEALED", "UNIQUE",
                                                 character_name, [],
                                                 self.gold_color, self.white_color):
            return False

        return True

    def _generate_and_reveal_race(self) -> bool:
        """Generate race and show animated reveal"""
        # Generate race
        race_data = self.generator.generate_race()
        self.character_data = self.generator.character_profile
        self.logger.log_race_generation(race_data)
        
        # Update character sheet
        self.character_sheet.character_data = self.character_data
        self.character_sheet.update_readable_stats(self.character_data)
        
        # Show popup reveal - make rarity and quality uppercase
        rarity_text = race_data['rarity'].upper()
        quality_text = race_data['quality'].upper()

        # Determine colors
        if race_data['quality'] == 'positive':
            quality_color = self.green_color
        elif race_data['quality'] == 'negative':
            quality_color = self.red_color
        else:
            quality_color = self.white_color

        # Format effects for display
        effects = []
        for stat, value in race_data.get('base_stats', {}).items():
            if value != 0:
                effects.append(f"{value:+d} {stat}")
        for stat, value in race_data.get('utility_stats', {}).items():
            if value != 0:
                effects.append(f"{value:+d} {stat}")

        # Add "no bonus" if no effects
        if not effects:
            effects = ["no bonus"]

        # Show interactive popup
        if not self._show_interactive_generation_popup(
            "Race", rarity_text, quality_text, race_data['name'],
            effects, self.gold_color, quality_color
        ):
            return False
        
        # Reveal character sheet lines
        self._reveal_character_lines(0, 5)  # Reveal first 5 lines
        
        return True
    
    def _generate_and_reveal_gender(self) -> bool:
        """Generate gender and show animated reveal"""
        # Generate gender
        gender_data = self.generator.generate_gender()
        self.character_data = self.generator.character_profile
        self.logger.log_gender_generation(gender_data)
        
        # Update character sheet
        self.character_sheet.character_data = self.character_data
        self.character_sheet.update_readable_stats(self.character_data)
        
        # Format gender effects
        effects = []
        for stat, value in gender_data.get('effects', {}).items():
            if isinstance(value, (int, float)) and value != 0:
                if abs(value) < 1:
                    effects.append(f"{value:+.0%} {stat}")
                else:
                    effects.append(f"{value:+d} {stat}")

        # Add "no bonus" if no effects
        if not effects:
            effects = ["no bonus"]

        # Show interactive popup - make text uppercase
        if not self._show_interactive_generation_popup(
            "Gender", "DETERMINED", "AVERAGE",
            f"{gender_data['icon']} {gender_data['name']}",
            effects, self.gold_color, self.white_color
        ):
            return False
        
        # Reveal more character sheet lines
        self._reveal_character_lines(5, 7)  # Reveal gender line
        
        return True
    
    def _generate_and_reveal_name(self) -> bool:
        """Generate name and show reveal"""
        # Generate name
        race_name = self.character_data['race']['name'].lower()
        gender_name = self.character_data['gender']['name'].lower()
        character_name = self.name_generator.generate_name(race_name, gender_name)
        
        self.character_data['name'] = character_name
        self.logger.log_name_generation(character_name)
        
        # Show popup reveal
        if not self._show_popup_sequence([
            ("Name Revealed", self.gold_color, 1.5),
            (character_name, self.white_color, 2.0)
        ]):
            return False
        
        return True
    
    def _show_interactive_generation_popup(self, category: str, rarity: str, quality: str,
                                          name: str, effects: list, rarity_color: tuple,
                                          quality_color: tuple) -> bool:
        """Show interactive generation popup with step-by-step reveal"""

        # Step 1: Show what we're generating
        if not self._show_step_popup(f"Generating {category}...", self.gold_color, auto_advance=True, duration=2.0):
            return False

        # Step 2: Show rarity
        self._play_sound('reveal')
        if not self._show_step_popup(rarity, rarity_color, auto_advance=True, duration=2.5):
            return False

        # Step 3: Show quality
        if quality != 'Average':
            sound_name = 'positive' if 'positive' in quality.lower() else 'negative'
            self._play_sound(sound_name)
        if not self._show_step_popup(quality, quality_color, auto_advance=True, duration=2.0):
            return False

        # Step 4: Show name and effects - wait for button press
        effects_text = ", ".join(effects) if effects else "No special effects"
        full_text = f"{name}\n\n{effects_text}\n\nPress any key to continue..."

        if not self._show_step_popup(full_text, self.white_color, auto_advance=False):
            return False

        return True

    def _show_generation_result_popup_with_image(self, category: str, rarity: str, quality: str,
                                               name: str, effects: list, rarity_color: tuple,
                                               quality_color: tuple, image: pygame.Surface) -> bool:
        """Show generation result popup with image"""
        # Create content lines
        content_lines = []
        content_lines.append((f"Generating {category}", self.fonts['popup_medium'], self.gold_color))
        content_lines.append(("", self.fonts['small'], self.white_color))  # Empty line
        content_lines.append((rarity, self.fonts['popup_large'], rarity_color))
        content_lines.append((quality, self.fonts['popup_medium'], quality_color))
        content_lines.append(("", self.fonts['small'], self.white_color))  # Empty line
        content_lines.append((name, self.fonts['popup_large'], self.white_color))

        if effects:
            content_lines.append(("", self.fonts['small'], self.white_color))  # Empty line
            content_lines.append(("Effects:", self.fonts['popup_medium'], self.gold_color))

            # Arrange effects in columns if many
            if len(effects) <= 4:
                # Single column
                for effect in effects:
                    content_lines.append((f"  {effect}", self.fonts['small'], self.white_color))
            else:
                # Multiple columns
                effects_per_col = 4
                cols = (len(effects) + effects_per_col - 1) // effects_per_col

                for row in range(effects_per_col):
                    line_parts = []
                    for col in range(cols):
                        idx = col * effects_per_col + row
                        if idx < len(effects):
                            line_parts.append(effects[idx])

                    if line_parts:
                        line_text = "  " + "    ".join(line_parts)
                        content_lines.append((line_text, self.fonts['small'], self.white_color))

        content_lines.append(("", self.fonts['small'], self.white_color))  # Empty line
        content_lines.append(("Press any key to continue...", self.fonts['small'], self.gold_color))

        # Show popup with image
        return self._show_multi_line_popup_with_image(content_lines, image)

    def _show_character_description(self, description: str, image: pygame.Surface) -> bool:
        """Show character description with character-by-character typing"""
        start_time = time.time()
        typing_speed = 0.05  # Time between characters

        while True:
            current_time = time.time()
            elapsed = current_time - start_time

            # Calculate how many characters to show
            chars_to_show = min(len(description), int(elapsed / typing_speed))
            visible_text = description[:chars_to_show]

            # Draw background
            self.screen.fill(self.bg_color)
            self.character_sheet.draw_character_sheet(self.reveal_progress)

            # Calculate popup size and position
            popup_width = 1000
            popup_height = 400
            popup_rect = pygame.Rect(
                (SCREEN_WIDTH - popup_width) // 2,
                (SCREEN_HEIGHT - popup_height) // 2,
                popup_width, popup_height
            )

            # Draw popup background
            popup_bg = pygame.Surface((popup_width, popup_height))
            popup_bg.fill((25, 25, 35))
            popup_bg.set_alpha(240)
            self.screen.blit(popup_bg, popup_rect)

            # Draw border
            pygame.draw.rect(self.screen, self.gold_color, popup_rect, 3)

            # Draw image on the left
            image_rect = pygame.Rect(popup_rect.x + 20, popup_rect.y + 20, 200, 200)
            scaled_image = pygame.transform.scale(image, (200, 200))
            self.screen.blit(scaled_image, image_rect)

            # Draw description text on the right with word wrapping
            text_area = pygame.Rect(popup_rect.x + 240, popup_rect.y + 20, popup_width - 260, popup_height - 80)

            if visible_text:
                # Word wrap the visible text
                words = visible_text.split(' ')
                lines = []
                current_line = ""

                for word in words:
                    test_line = current_line + (" " if current_line else "") + word
                    test_surface = self.fonts['medium'].render(test_line, True, self.white_color)
                    if test_surface.get_width() <= text_area.width:
                        current_line = test_line
                    else:
                        if current_line:
                            lines.append(current_line)
                        current_line = word

                if current_line:
                    lines.append(current_line)

                # Draw lines
                for i, line in enumerate(lines):
                    if i * 25 < text_area.height - 25:  # Make sure we don't overflow
                        line_surface = self.fonts['medium'].render(line, True, self.white_color)
                        self.screen.blit(line_surface, (text_area.x, text_area.y + i * 25))

            # Draw continue prompt if typing is complete
            if chars_to_show >= len(description):
                prompt_surface = self.fonts['small'].render("Press any key to continue...", True, self.gold_color)
                prompt_rect = prompt_surface.get_rect(centerx=popup_rect.centerx, bottom=popup_rect.bottom - 20)
                self.screen.blit(prompt_surface, prompt_rect)

            pygame.display.flip()
            pygame.time.wait(30)

            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return False
                elif event.type == pygame.KEYDOWN or event.type == pygame.MOUSEBUTTONDOWN:
                    if chars_to_show >= len(description):
                        self._play_sound('click')
                        return True

        return True

    def _show_generation_result_popup(self, category: str, rarity: str, quality: str,
                                     name: str, effects: list, rarity_color: tuple,
                                     quality_color: tuple) -> bool:
        """Show generation result in a single popup with all information"""

        # Create content lines
        content_lines = []
        content_lines.append((f"Generating {category}", self.fonts['popup_medium'], self.gold_color))
        content_lines.append(("", self.fonts['small'], self.white_color))  # Empty line
        content_lines.append((rarity, self.fonts['popup_large'], rarity_color))
        content_lines.append((quality, self.fonts['popup_medium'], quality_color))
        content_lines.append(("", self.fonts['small'], self.white_color))  # Empty line
        content_lines.append((name, self.fonts['popup_large'], self.white_color))

        if effects:
            content_lines.append(("", self.fonts['small'], self.white_color))  # Empty line
            content_lines.append(("Effects:", self.fonts['popup_medium'], self.gold_color))

            # Arrange effects in columns if many
            if len(effects) <= 4:
                # Single column
                for effect in effects:
                    content_lines.append((f"  {effect}", self.fonts['small'], self.white_color))
            else:
                # Multiple columns
                effects_per_col = 4
                cols = (len(effects) + effects_per_col - 1) // effects_per_col

                for row in range(effects_per_col):
                    line_parts = []
                    for col in range(cols):
                        idx = col * effects_per_col + row
                        if idx < len(effects):
                            line_parts.append(effects[idx])

                    if line_parts:
                        line_text = "  " + "    ".join(line_parts)
                        content_lines.append((line_text, self.fonts['small'], self.white_color))

        content_lines.append(("", self.fonts['small'], self.white_color))  # Empty line
        content_lines.append(("Press any key to continue...", self.fonts['small'], self.gold_color))

        # Show popup
        return self._show_multi_line_popup(content_lines)

    def _show_multi_line_popup(self, content_lines: list) -> bool:
        """Show popup with multiple lines of content and animated text reveal"""
        start_time = time.time()
        text_reveal_time = 0.5  # Time to reveal each line

        while True:
            current_time = time.time()
            elapsed = current_time - start_time

            # Fade in effect
            if elapsed < 0.3:
                alpha = int(255 * (elapsed / 0.3))
            else:
                alpha = 255

            # Draw background
            self.screen.fill(self.bg_color)
            self.character_sheet.draw_character_sheet(self.reveal_progress)

            # Calculate popup size
            popup_width = 900
            popup_height = len(content_lines) * 35 + 80
            popup_window_rect = pygame.Rect(
                (SCREEN_WIDTH - popup_width) // 2,
                (SCREEN_HEIGHT - popup_height) // 2,
                popup_width, popup_height
            )

            # Draw popup background with glow effect
            popup_bg = pygame.Surface((popup_width, popup_height))
            popup_bg.fill((20, 25, 35))
            popup_bg.set_alpha(alpha)
            self.screen.blit(popup_bg, popup_window_rect)

            # Draw glowing border
            for i in range(3):
                border_color = (
                    min(255, self.gold_color[0] + 30 - i * 10),
                    min(255, self.gold_color[1] + 30 - i * 10),
                    min(255, self.gold_color[2] + 30 - i * 10)
                )
                border_rect = popup_window_rect.inflate(i * 2, i * 2)
                pygame.draw.rect(self.screen, border_color, border_rect, 2)

            # Draw content lines with transparency and size effects
            y_offset = 40
            for line_idx, (text, font, color) in enumerate(content_lines):
                if text.strip():  # Skip empty lines for rendering
                    # Calculate how much of this line to show
                    line_start_time = 0.3 + line_idx * text_reveal_time
                    line_progress = max(0, min(1, (elapsed - line_start_time) / text_reveal_time))

                    if line_progress > 0:
                        # Calculate transparency and size effects
                        if line_progress < 0.7:
                            # Growing phase
                            text_alpha = int(255 * (line_progress / 0.7))
                            scale_factor = 0.5 + 0.7 * (line_progress / 0.7)  # Grow from 0.5x to 1.2x
                        elif line_progress < 0.9:
                            # Peak phase
                            text_alpha = 255
                            scale_factor = 1.2
                        else:
                            # Shrinking phase
                            text_alpha = 255
                            shrink_progress = (line_progress - 0.9) / 0.1
                            scale_factor = 1.2 - 0.2 * shrink_progress  # Shrink from 1.2x to 1.0x

                        # Render text with effects
                        text_surface = font.render(text, True, color)

                        # Scale the text surface
                        if scale_factor != 1.0:
                            original_size = text_surface.get_size()
                            new_size = (int(original_size[0] * scale_factor), int(original_size[1] * scale_factor))
                            text_surface = pygame.transform.scale(text_surface, new_size)

                        # Apply transparency
                        text_surface.set_alpha(min(alpha, text_alpha))

                        if "Press any key" in text:
                            text_rect = text_surface.get_rect(centerx=popup_window_rect.centerx,
                                                            y=popup_window_rect.y + y_offset)
                        else:
                            text_rect = text_surface.get_rect(centerx=popup_window_rect.centerx,
                                                            y=popup_window_rect.y + y_offset)

                        self.screen.blit(text_surface, text_rect)

                        # Play sound when line starts appearing
                        if line_progress > 0 and not getattr(self, f'_sound_played_{line_idx}', False):
                            self._play_sound('click')
                            setattr(self, f'_sound_played_{line_idx}', True)

                y_offset += 35

            pygame.display.flip()
            pygame.time.wait(30)  # Slower for better effect

            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return False
                elif event.type == pygame.KEYDOWN or event.type == pygame.MOUSEBUTTONDOWN:
                    # Only allow continue if all text is revealed
                    total_reveal_time = 0.3 + len(content_lines) * text_reveal_time
                    if elapsed >= total_reveal_time:
                        self._play_sound('click')
                        # Clear sound flags
                        for i in range(len(content_lines)):
                            if hasattr(self, f'_sound_played_{i}'):
                                delattr(self, f'_sound_played_{i}')
                        return True

        return True

    def _show_multi_line_popup_with_image(self, content_lines: list, image: pygame.Surface) -> bool:
        """Show popup with multiple lines of content, animated text reveal, and image"""
        start_time = time.time()
        text_reveal_time = 0.5  # Time to reveal each line

        while True:
            current_time = time.time()
            elapsed = current_time - start_time

            # Fade in effect
            if elapsed < 0.3:
                alpha = int(255 * (elapsed / 0.3))
            else:
                alpha = 255

            # Draw background
            self.screen.fill(self.bg_color)
            self.character_sheet.draw_character_sheet(self.reveal_progress)

            # Calculate popup size (wider for image)
            popup_width = 1100
            popup_height = len(content_lines) * 35 + 80
            popup_window_rect = pygame.Rect(
                (SCREEN_WIDTH - popup_width) // 2,
                (SCREEN_HEIGHT - popup_height) // 2,
                popup_width, popup_height
            )

            # Draw popup background with glow effect
            popup_bg = pygame.Surface((popup_width, popup_height))
            popup_bg.fill((20, 25, 35))
            popup_bg.set_alpha(alpha)
            self.screen.blit(popup_bg, popup_window_rect)

            # Draw glowing border
            for i in range(3):
                border_color = (
                    min(255, self.gold_color[0] + 30 - i * 10),
                    min(255, self.gold_color[1] + 30 - i * 10),
                    min(255, self.gold_color[2] + 30 - i * 10)
                )
                border_rect = popup_window_rect.inflate(i * 2, i * 2)
                pygame.draw.rect(self.screen, border_color, border_rect, 2)

            # Draw image on the left side
            image_rect = pygame.Rect(popup_window_rect.x + 20, popup_window_rect.y + 20, 200, 200)
            scaled_image = pygame.transform.scale(image, (200, 200))
            scaled_image.set_alpha(alpha)
            self.screen.blit(scaled_image, image_rect)

            # Draw content lines with transparency and size effects (offset for image)
            y_offset = 40
            text_start_x = popup_window_rect.x + 240  # Start text after image
            for line_idx, (text, font, color) in enumerate(content_lines):
                if text.strip():  # Skip empty lines for rendering
                    # Calculate how much of this line to show
                    line_start_time = 0.3 + line_idx * text_reveal_time
                    line_progress = max(0, min(1, (elapsed - line_start_time) / text_reveal_time))

                    if line_progress > 0:
                        # Calculate transparency and size effects
                        if line_progress < 0.7:
                            # Growing phase
                            text_alpha = int(255 * (line_progress / 0.7))
                            scale_factor = 0.5 + 0.7 * (line_progress / 0.7)  # Grow from 0.5x to 1.2x
                        elif line_progress < 0.9:
                            # Peak phase
                            text_alpha = 255
                            scale_factor = 1.2
                        else:
                            # Shrinking phase
                            text_alpha = 255
                            shrink_progress = (line_progress - 0.9) / 0.1
                            scale_factor = 1.2 - 0.2 * shrink_progress  # Shrink from 1.2x to 1.0x

                        # Render text with effects
                        text_surface = font.render(text, True, color)

                        # Scale the text surface
                        if scale_factor != 1.0:
                            original_size = text_surface.get_size()
                            new_size = (int(original_size[0] * scale_factor), int(original_size[1] * scale_factor))
                            text_surface = pygame.transform.scale(text_surface, new_size)

                        # Apply transparency
                        text_surface.set_alpha(min(alpha, text_alpha))

                        if "Press any key" in text:
                            text_rect = text_surface.get_rect(centerx=popup_window_rect.centerx,
                                                            y=popup_window_rect.y + y_offset)
                        else:
                            text_rect = text_surface.get_rect(x=text_start_x,
                                                            y=popup_window_rect.y + y_offset)

                        self.screen.blit(text_surface, text_rect)

                        # Play sound when line starts appearing
                        if line_progress > 0 and not getattr(self, f'_sound_played_{line_idx}', False):
                            self._play_sound('click')
                            setattr(self, f'_sound_played_{line_idx}', True)

                y_offset += 35

            pygame.display.flip()
            pygame.time.wait(30)  # Slower for better effect

            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return False
                elif event.type == pygame.KEYDOWN or event.type == pygame.MOUSEBUTTONDOWN:
                    # Only allow continue if all text is revealed
                    total_reveal_time = 0.3 + len(content_lines) * text_reveal_time
                    if elapsed >= total_reveal_time:
                        self._play_sound('click')
                        # Clear sound flags
                        for i in range(len(content_lines)):
                            if hasattr(self, f'_sound_played_{i}'):
                                delattr(self, f'_sound_played_{i}')
                        return True

        return True

    def _show_step_popup(self, text: str, color: tuple, auto_advance: bool = True, duration: float = 3.0) -> bool:
        """Show a single step popup"""
        start_time = time.time()

        while True:
            current_time = time.time()
            elapsed = current_time - start_time

            # Auto advance after duration if enabled
            if auto_advance and elapsed >= duration:
                break

            # Calculate fade in effect
            if elapsed < 0.5:
                alpha = int(255 * (elapsed / 0.5))
            else:
                alpha = 255

            # Draw background
            self.screen.fill(self.bg_color)
            self.character_sheet.draw_character_sheet(self.reveal_progress)

            # Create popup window
            popup_width = 700
            popup_height = 300
            popup_window_rect = pygame.Rect(
                (SCREEN_WIDTH - popup_width) // 2,
                (SCREEN_HEIGHT - popup_height) // 2,
                popup_width, popup_height
            )

            # Draw popup background
            popup_bg = pygame.Surface((popup_width, popup_height))
            popup_bg.fill((25, 25, 35))
            popup_bg.set_alpha(alpha)
            self.screen.blit(popup_bg, popup_window_rect)

            # Draw border
            pygame.draw.rect(self.screen, color, popup_window_rect, 3)

            # Draw text (handle multiline)
            lines = text.split('\n')
            line_height = 40
            total_height = len(lines) * line_height
            start_y = popup_window_rect.centery - total_height // 2

            for i, line in enumerate(lines):
                if line.strip():
                    if "Press any key" in line:
                        font = self.fonts['small']
                        line_color = self.gold_color
                    elif i == 0:  # First line (main text)
                        font = self.fonts['popup_large']
                        line_color = color
                    else:
                        font = self.fonts['popup_medium']
                        line_color = self.white_color

                    text_surface = font.render(line, True, line_color)
                    text_surface.set_alpha(alpha)
                    text_rect = text_surface.get_rect(centerx=popup_window_rect.centerx,
                                                    y=start_y + i * line_height)
                    self.screen.blit(text_surface, text_rect)

            pygame.display.flip()
            pygame.time.wait(20)

            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return False
                elif not auto_advance and (event.type == pygame.KEYDOWN or event.type == pygame.MOUSEBUTTONDOWN):
                    self._play_sound('click')
                    return True

        return True
    
    def _show_popup(self, text: str, color: tuple, duration: float) -> bool:
        """Show enhanced animated popup text in center window"""
        start_time = time.time()

        while time.time() - start_time < duration:
            progress = (time.time() - start_time) / duration

            # Enhanced fade in/out with longer visibility
            if progress < 0.2:
                alpha = int(255 * (progress / 0.2))
                scale = 0.8 + 0.2 * (progress / 0.2)
            elif progress > 0.8:
                alpha = int(255 * ((1.0 - progress) / 0.2))
                scale = 1.0
            else:
                alpha = 255
                scale = 1.0

            # Draw background
            self.screen.fill(self.bg_color)
            self.character_sheet.draw_character_sheet(self.reveal_progress)

            # Create enhanced popup window
            popup_width = 600
            popup_height = 200
            popup_window_rect = pygame.Rect(
                (SCREEN_WIDTH - popup_width) // 2,
                (SCREEN_HEIGHT - popup_height) // 2,
                popup_width, popup_height
            )

            # Draw popup window background with gradient effect
            popup_bg = pygame.Surface((popup_width, popup_height))
            popup_bg.fill((30, 30, 45))

            # Add gradient effect
            for i in range(popup_height):
                gradient_color = (
                    30 + int(15 * (i / popup_height)),
                    30 + int(15 * (i / popup_height)),
                    45 + int(20 * (i / popup_height))
                )
                pygame.draw.line(popup_bg, gradient_color, (0, i), (popup_width, i))

            popup_bg.set_alpha(alpha)
            self.screen.blit(popup_bg, popup_window_rect)

            # Draw border with glow effect
            for i in range(3):
                border_color = (
                    min(255, color[0] + 50),
                    min(255, color[1] + 50),
                    min(255, color[2] + 50)
                )
                border_rect = popup_window_rect.inflate(i * 2, i * 2)
                pygame.draw.rect(self.screen, border_color, border_rect, 2)

            # Draw text with scaling effect
            font_size = int(self.fonts['popup_large'].get_height() * scale)
            if font_size > 0:
                scaled_font = pygame.font.Font(None, font_size)
                popup_surface = scaled_font.render(text, True, color)
                popup_surface.set_alpha(alpha)
                popup_rect = popup_surface.get_rect(center=popup_window_rect.center)
                self.screen.blit(popup_surface, popup_rect)

            # Add sparkle effects around the popup
            if progress > 0.1 and progress < 0.9:
                self._draw_sparkle_effects(popup_window_rect, progress)

            pygame.display.flip()
            pygame.time.wait(20)  # Slower animation

            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return False

        return True

    def _draw_sparkle_effects(self, popup_rect: pygame.Rect, progress: float):
        """Draw sparkle effects around the popup"""
        import random

        sparkle_count = 8
        for i in range(sparkle_count):
            # Calculate sparkle position around popup
            angle = (i / sparkle_count) * 2 * math.pi + progress * 2
            radius = 150 + 30 * math.sin(progress * 4)

            sparkle_x = popup_rect.centerx + int(radius * math.cos(angle))
            sparkle_y = popup_rect.centery + int(radius * math.sin(angle))

            # Draw sparkle
            sparkle_size = 3 + int(2 * math.sin(progress * 6 + i))
            sparkle_color = (255, 215 + int(40 * math.sin(progress * 3)), 0)  # Golden sparkles

            if 0 <= sparkle_x < SCREEN_WIDTH and 0 <= sparkle_y < SCREEN_HEIGHT:
                pygame.draw.circle(self.screen, sparkle_color, (sparkle_x, sparkle_y), sparkle_size)

    def _reveal_dna_traits(self) -> bool:
        """Reveal DNA traits one by one"""
        dna_traits = self.character_data.get('dna_traits', {})

        for i, (category, trait_data) in enumerate(dna_traits.items()):
            rarity_text = trait_data['rarity'].title()
            quality_text = trait_data['quality'].title() if trait_data['quality'] != 'average' else 'Average'

            # Determine colors
            if trait_data['quality'] == 'positive':
                quality_color = self.green_color
            elif trait_data['quality'] == 'negative':
                quality_color = self.red_color
            else:
                quality_color = self.white_color

            # Format trait effects
            effects = []
            for stat, value in trait_data['effects'].items():
                if isinstance(value, (int, float)) and value != 0:
                    if abs(value) < 1:
                        effects.append(f"{value:+.0%} {stat}")
                    else:
                        effects.append(f"{value:+d} {stat}")

            # Add elemental affinities if present
            if trait_data.get('magic_elements'):
                from .dna_traits import MAGIC_ELEMENTS
                for element in trait_data['magic_elements']:
                    element_data = MAGIC_ELEMENTS[element]
                    effects.append(f"{element_data['icon']} {element_data['name']} Affinity")

            # Show popup
            if not self._show_generation_result_popup(
                trait_data['category_name'], rarity_text, quality_text,
                trait_data['name'], effects, self.gold_color, quality_color
            ):
                return False

            # Reveal corresponding lines
            self._reveal_character_lines(7 + i * 2, 9 + i * 2)

        return True

    def _reveal_background_history(self) -> bool:
        """Reveal background history events"""
        background_history = self.character_data.get('background_history', [])

        if not background_history:
            return True

        # Show initial popup
        if not self._show_step_popup("Revealing Life's Journey...", self.gold_color, auto_advance=True, duration=2.0):
            return False

        # Show each life event
        current_age = -1

        for event in background_history:
            # Skip growth events for display
            if event.get('type') == 'growth':
                continue

            # Show age transition if needed
            if event['age'] != current_age:
                current_age = event['age']
                if not self._show_step_popup(f"Age {current_age}", self.gold_color, auto_advance=True, duration=1.0):
                    return False

            # Format event for popup
            effects = []
            if event.get('effects'):
                for stat, value in event['effects'].items():
                    effects.append(f"{value:+d} {stat}")

            if event.get('traits_gained'):
                for trait in event['traits_gained']:
                    effects.append(f"Trait: {trait}")

            if event.get('skills_gained'):
                for skill in event['skills_gained']:
                    effects.append(f"Skill: {skill['name']} (Lv{skill.get('level', 1)})")

            # Show event popup
            if not self._show_generation_result_popup(
                f"Age {current_age} Event", "Life Event", "Significant",
                event['event'], effects, self.gold_color, self.white_color
            ):
                return False

        return True

    def _save_character(self) -> bool:
        """Save character to file"""
        try:
            import json
            import os
            from datetime import datetime

            # Create saves directory if it doesn't exist
            if not os.path.exists('saves'):
                os.makedirs('saves')

            # Create filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            character_name = self.character_data.get('name', 'NO_NAME').replace(' ', '_')
            filename = f"saves/character_{character_name}_{timestamp}.json"

            # Prepare character save data
            save_data = {
                'character_info': {
                    'name': self.character_data.get('name', 'NO NAME'),
                    'race': self.character_data.get('race', {}),
                    'gender': self.character_data.get('gender', {}),
                    'creation_date': datetime.now().isoformat()
                },
                'stats': self.character_data.get('effective_stats', {}),
                'dna_traits': self.character_data.get('dna_traits', {}),
                'background_history': self.character_data.get('background_history', []),
                'full_character_data': self.character_data
            }

            # Save to file
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False, default=str)

            # Show save confirmation
            if not self._show_step_popup(f"Character saved as:\n{filename}", self.gold_color, auto_advance=True, duration=2.0):
                return False

            return True

        except Exception as e:
            print(f"Error saving character: {e}")
            return True  # Continue even if save fails

    def _reveal_character_lines(self, start_line: int, end_line: int):
        """Reveal character sheet lines with animation"""
        for line in range(start_line, min(end_line, len(self.character_sheet.readable_lines))):
            self.reveal_progress = line / len(self.character_sheet.readable_lines)
            
            # Show reveal animation
            for frame in range(30):  # 0.5 second animation
                self.screen.fill(self.bg_color)
                self.character_sheet.draw_character_sheet(self.reveal_progress)
                pygame.display.flip()
                pygame.time.wait(16)
                
                # Handle events
                for event in pygame.event.get():
                    if event.type == pygame.QUIT:
                        return False

    def _generate_and_reveal_dna_traits(self) -> bool:
        """Generate DNA traits and show reveals"""
        dna_traits = self.generator.generate_all_dna_traits()
        self.character_data = self.generator.character_profile

        # Log each trait
        for trait_data in dna_traits.values():
            self.logger.log_dna_trait_generation(trait_data)

        # Update character sheet
        self.character_sheet.character_data = self.character_data
        self.character_sheet.update_readable_stats(self.character_data)

        # Show each trait reveal
        for i, (category, trait_data) in enumerate(dna_traits.items()):
            rarity_text = trait_data['rarity'].title()
            quality_text = trait_data['quality'].title() if trait_data['quality'] != 'average' else 'Average'

            # Determine colors
            if trait_data['quality'] == 'positive':
                quality_color = self.green_color
            elif trait_data['quality'] == 'negative':
                quality_color = self.red_color
            else:
                quality_color = self.white_color

            # Format trait effects
            effects = []
            for stat, value in trait_data['effects'].items():
                if isinstance(value, (int, float)) and value != 0:
                    if abs(value) < 1:
                        effects.append(f"{value:+.0%} {stat}")
                    else:
                        effects.append(f"{value:+d} {stat}")

            # Add elemental affinities if present
            if trait_data.get('magic_elements'):
                from .dna_traits import MAGIC_ELEMENTS
                for element in trait_data['magic_elements']:
                    element_data = MAGIC_ELEMENTS[element]
                    effects.append(f"{element_data['icon']} {element_data['name']} Affinity")

            # Show interactive popup
            if not self._show_interactive_generation_popup(
                trait_data['category_name'], rarity_text, quality_text,
                trait_data['name'], effects, self.gold_color, quality_color
            ):
                return False

            # Reveal corresponding lines
            self._reveal_character_lines(7 + i * 2, 9 + i * 2)

        return True

    def _generate_background_history(self) -> bool:
        """Generate background history with detailed year-by-year display"""
        try:
            from .background_history_generator import BackgroundHistoryGenerator

            # Show initial popup
            if not self._show_step_popup("Weaving Life's Tapestry...", self.gold_color, auto_advance=True, duration=2.0):
                return False

            history_gen = BackgroundHistoryGenerator(self.character_data)
            background_history = history_gen.generate_full_background()
            self.character_data = history_gen.get_updated_character()

            # Show each life event year by year
            if not self._show_background_events(background_history):
                return False

            return True
        except Exception as e:
            print(f"Background generation error: {e}")
            return True  # Continue even if background fails

    def _show_background_events(self, background_history: list) -> bool:
        """Show background events year by year"""
        if not background_history:
            return True

        current_age = -1

        for event in background_history:
            # Skip growth events for display
            if event.get('type') == 'growth':
                continue

            # Show age transition if needed
            if event['age'] != current_age:
                current_age = event['age']
                age_text = f"Age {current_age}"
                if not self._show_step_popup(age_text, self.gold_color, auto_advance=True, duration=1.5):
                    return False

            # Format event text
            event_text = f"{event['event']}\n\n{event.get('description', '')}"

            # Add effects if any
            if event.get('effects'):
                effects_text = ", ".join([f"{v:+d} {k}" for k, v in event['effects'].items()])
                event_text += f"\n\nEffects: {effects_text}"

            # Add traits gained
            if event.get('traits_gained'):
                traits_text = ", ".join(event['traits_gained'])
                event_text += f"\n\nTraits Gained: {traits_text}"

            # Add skills gained
            if event.get('skills_gained'):
                skills_text = ", ".join([f"{s['name']} (Lv{s.get('level', 1)})" for s in event['skills_gained']])
                event_text += f"\n\nSkills Gained: {skills_text}"

            event_text += "\n\nPress any key to continue..."

            # Show event popup
            if not self._show_step_popup(event_text, self.white_color, auto_advance=False):
                return False

        return True

    def _final_character_reveal(self) -> bool:
        """Final character reveal with complete sheet"""
        # Update final character sheet
        self.character_sheet.character_data = self.character_data
        self.character_sheet.update_readable_stats(self.character_data)

        # Reveal all remaining lines
        self._reveal_character_lines(0, len(self.character_sheet.readable_lines))

        # Show completion popup
        if not self._show_step_popup("🎭 DESTINY SEALED 🎭", self.gold_color, auto_advance=True, duration=3.0):
            return False
        if not self._show_step_popup("Your One Life Begins...", self.white_color, auto_advance=True, duration=2.0):
            return False

        # Show final character sheet - wait for ESC
        while True:
            self.screen.fill(self.bg_color)
            self.character_sheet.draw_character_sheet(1.0)

            # Draw completion message
            complete_text = self.fonts['medium'].render("Press ESC to exit character generation", True, self.gold_color)
            complete_rect = complete_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT - 50))
            self.screen.blit(complete_text, complete_rect)

            pygame.display.flip()
            pygame.time.wait(16)

            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        return True

        return True
