"""
Full-screen character generation UI with improved text effects and layout
"""

import pygame
import time
import os
import math
from typing import Dict, Any, Optional, List, Tuple
from .character_generator import CharacterGenerator
from .character_logger import CharacterLogger
from .name_generator import NameGenerator
from .races import RACES

# Screen constants
SCREEN_WIDTH = 1280
SCREEN_HEIGHT = 720

class FullscreenCharacterGeneratorUI:
    def __init__(self, screen: pygame.Surface):
        self.screen = screen
        self.generator = CharacterGenerator()
        self.logger = CharacterLogger()
        self.name_generator = NameGenerator()
        
        # Load fonts
        self._load_fonts()
        
        # Animation state
        self.current_step = 0
        self.reveal_progress = 0.0
        self.text_effects = {}
        
        # Sound system
        self._init_sounds()
        
        # Colors
        self.bg_color = (15, 15, 25)
        self.panel_bg = (25, 25, 35)
        self.border_color = (60, 60, 80)
        self.gold_color = (255, 215, 0)
        self.green_color = (50, 205, 50)
        self.red_color = (220, 20, 60)
        self.white_color = (255, 255, 255)
        self.text_color = (200, 200, 220)
        
        # Layout
        self.left_panel_width = 400
        self.right_panel_width = 500
        self.center_gap = 50
        
        # Generated data storage
        self.generated_data = []
        self.character_data = None
        
    def _load_fonts(self):
        """Load fonts for the UI"""
        try:
            self.fonts = {
                'title': pygame.font.Font(None, 48),
                'large': pygame.font.Font(None, 36),
                'medium': pygame.font.Font(None, 28),
                'small': pygame.font.Font(None, 20),
                'tiny': pygame.font.Font(None, 16)
            }
        except:
            # Fallback fonts
            self.fonts = {
                'title': pygame.font.Font(None, 48),
                'large': pygame.font.Font(None, 36),
                'medium': pygame.font.Font(None, 28),
                'small': pygame.font.Font(None, 20),
                'tiny': pygame.font.Font(None, 16)
            }
    
    def _init_sounds(self):
        """Initialize sound system"""
        try:
            pygame.mixer.init()
            self.sounds = {
                'reveal': None,
                'click': None,
                'positive': None,
                'negative': None
            }
        except:
            self.sounds = {}
    
    def _play_sound(self, sound_name: str):
        """Play a sound effect"""
        try:
            if sound_name in self.sounds and self.sounds[sound_name]:
                self.sounds[sound_name].play()
        except:
            pass
    
    def load_race_image(self, race_name: str) -> pygame.Surface:
        """Load race image"""
        # Convert race name to filename format
        race_filename = race_name.lower().replace('-', '-').replace(' ', '-')
        
        # Try to load race image
        image_path = f"pictures/race/{race_filename}.png"
        if os.path.exists(image_path):
            try:
                image = pygame.image.load(image_path)
                return pygame.transform.scale(image, (300, 300))
            except:
                pass
        
        # Use unknown placeholder
        unknown_path = "pictures/race/unkown.png"
        if os.path.exists(unknown_path):
            try:
                image = pygame.image.load(unknown_path)
                return pygame.transform.scale(image, (300, 300))
            except:
                pass
        
        # Create placeholder
        placeholder = pygame.Surface((300, 300))
        placeholder.fill((60, 60, 80))
        return placeholder
    
    def load_gender_image(self, gender_name: str) -> pygame.Surface:
        """Load gender image"""
        # Use the correct gender images from pictures/gender/
        image_path = f"pictures/gender/{gender_name.lower()}.png"
        if os.path.exists(image_path):
            try:
                image = pygame.image.load(image_path)
                return pygame.transform.scale(image, (100, 100))
            except:
                pass
        
        # Use unknown placeholder
        unknown_path = "pictures/gender/unkown.png"
        if os.path.exists(unknown_path):
            try:
                image = pygame.image.load(unknown_path)
                return pygame.transform.scale(image, (100, 100))
            except:
                pass
        
        # Create placeholder
        placeholder = pygame.Surface((100, 100))
        placeholder.fill((60, 60, 80))
        return placeholder
    
    def create_text_effect(self, text: str, font: pygame.font.Font, color: tuple, 
                          duration: float = 2.0) -> Dict[str, Any]:
        """Create a text effect with transparency and size changes"""
        return {
            'text': text,
            'font': font,
            'color': color,
            'duration': duration,
            'start_time': time.time(),
            'phase': 'fade_in'  # fade_in, hold, fade_out
        }
    
    def render_text_effect(self, effect: Dict[str, Any], x: int, y: int) -> bool:
        """Render text effect with transparency and size changes. Returns True if effect is complete."""
        current_time = time.time()
        elapsed = current_time - effect['start_time']
        
        if elapsed >= effect['duration']:
            return True
        
        # Calculate effect parameters
        progress = elapsed / effect['duration']
        
        if progress < 0.3:
            # Fade in with size growth
            alpha = int(255 * (progress / 0.3))
            scale = 0.5 + 0.7 * (progress / 0.3)
        elif progress < 0.7:
            # Hold phase
            alpha = 255
            scale = 1.2
        else:
            # Fade out with size reduction
            fade_progress = (progress - 0.7) / 0.3
            alpha = 255
            scale = 1.2 - 0.2 * fade_progress
        
        # Create scaled font
        base_size = effect['font'].get_height()
        new_size = max(1, int(base_size * scale))
        scaled_font = pygame.font.Font(None, new_size)
        
        # Render text
        text_surface = scaled_font.render(effect['text'], True, effect['color'])
        text_surface.set_alpha(alpha)
        
        # Center the text
        text_rect = text_surface.get_rect(center=(x, y))
        self.screen.blit(text_surface, text_rect)
        
        return False
    
    def draw_left_panel(self):
        """Draw the left panel with generated data"""
        panel_rect = pygame.Rect(20, 20, self.left_panel_width, SCREEN_HEIGHT - 40)
        pygame.draw.rect(self.screen, self.panel_bg, panel_rect)
        pygame.draw.rect(self.screen, self.border_color, panel_rect, 2)
        
        # Title
        title_surface = self.fonts['large'].render("Generated Data", True, self.gold_color)
        self.screen.blit(title_surface, (panel_rect.x + 20, panel_rect.y + 20))
        
        # Display generated data
        y_offset = 70
        for data in self.generated_data:
            if y_offset > panel_rect.height - 50:
                break
                
            # Data type
            type_surface = self.fonts['medium'].render(data['type'], True, self.white_color)
            self.screen.blit(type_surface, (panel_rect.x + 20, panel_rect.y + y_offset))
            y_offset += 30
            
            # Data details
            if 'image' in data:
                self.screen.blit(data['image'], (panel_rect.x + 20, panel_rect.y + y_offset))
                y_offset += data['image'].get_height() + 10
            
            if 'details' in data:
                for detail in data['details']:
                    detail_surface = self.fonts['small'].render(detail, True, self.text_color)
                    self.screen.blit(detail_surface, (panel_rect.x + 30, panel_rect.y + y_offset))
                    y_offset += 20
            
            y_offset += 20
    
    def draw_right_panel(self, title: str, content: List[str] = None):
        """Draw the right panel with current generation"""
        panel_x = SCREEN_WIDTH - self.right_panel_width - 20
        panel_rect = pygame.Rect(panel_x, 20, self.right_panel_width, SCREEN_HEIGHT - 40)
        pygame.draw.rect(self.screen, self.panel_bg, panel_rect)
        pygame.draw.rect(self.screen, self.border_color, panel_rect, 2)
        
        # Title
        title_surface = self.fonts['large'].render(title, True, self.gold_color)
        title_rect = title_surface.get_rect(centerx=panel_rect.centerx, y=panel_rect.y + 30)
        self.screen.blit(title_surface, title_rect)
        
        # Content
        if content:
            y_offset = 100
            for line in content:
                line_surface = self.fonts['medium'].render(line, True, self.text_color)
                line_rect = line_surface.get_rect(centerx=panel_rect.centerx, y=panel_rect.y + y_offset)
                self.screen.blit(line_surface, line_rect)
                y_offset += 35
    
    def draw_center_image(self, image: pygame.Surface):
        """Draw the main image in the center"""
        center_x = self.left_panel_width + 40 + (SCREEN_WIDTH - self.left_panel_width - self.right_panel_width - 80) // 2
        center_y = SCREEN_HEIGHT // 2
        
        image_rect = image.get_rect(center=(center_x, center_y))
        self.screen.blit(image, image_rect)
        
        # Draw border
        pygame.draw.rect(self.screen, self.border_color, image_rect, 3)

    def wait_for_keypress(self, prompt: str = "Press any key to continue...") -> bool:
        """Wait for user keypress"""
        waiting = True
        while waiting:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        return False
                    else:
                        self._play_sound('click')
                        return True

            # Draw prompt
            prompt_surface = self.fonts['medium'].render(prompt, True, self.gold_color)
            prompt_rect = prompt_surface.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT - 50))

            # Blinking effect
            alpha = int(128 + 127 * math.sin(time.time() * 3))
            prompt_surface.set_alpha(alpha)

            self.screen.blit(prompt_surface, prompt_rect)
            pygame.display.flip()
            pygame.time.wait(50)

        return False

    def generate_and_reveal_race(self) -> bool:
        """Generate and reveal race with new UI"""
        # Generate race
        race_data = self.generator.generate_race()
        self.character_data = self.generator.character_profile
        self.logger.log_race_generation(race_data)

        # Load race image
        race_image = self.load_race_image(race_data['name'])

        # Phase 1: Show "Generating Race..."
        self.screen.fill(self.bg_color)
        self.draw_left_panel()
        self.draw_right_panel("Generating Race...")
        self.draw_center_image(race_image)
        pygame.display.flip()
        time.sleep(1.5)

        # Phase 2: Show rarity with text effect
        rarity_text = race_data['rarity'].upper()
        rarity_effect = self.create_text_effect(rarity_text, self.fonts['title'], self.gold_color, 3.0)

        start_time = time.time()
        while time.time() - start_time < 3.0:
            self.screen.fill(self.bg_color)
            self.draw_left_panel()
            self.draw_right_panel("Race Generation")
            self.draw_center_image(race_image)

            # Render rarity effect
            center_x = self.left_panel_width + 40 + (SCREEN_WIDTH - self.left_panel_width - self.right_panel_width - 80) // 2
            self.render_text_effect(rarity_effect, center_x, 150)

            pygame.display.flip()
            pygame.time.wait(50)

        # Phase 3: Show quality with text effect
        quality_text = race_data['quality'].upper()
        if quality_text == 'AVERAGE':
            quality_text = 'AVERAGE'
            quality_color = self.white_color
        elif quality_text == 'POSITIVE':
            quality_color = self.green_color
        else:  # negative
            quality_color = self.red_color

        quality_effect = self.create_text_effect(quality_text, self.fonts['large'], quality_color, 3.0)

        start_time = time.time()
        while time.time() - start_time < 3.0:
            self.screen.fill(self.bg_color)
            self.draw_left_panel()
            self.draw_right_panel("Race Generation")
            self.draw_center_image(race_image)

            # Render quality effect
            center_x = self.left_panel_width + 40 + (SCREEN_WIDTH - self.left_panel_width - self.right_panel_width - 80) // 2
            self.render_text_effect(quality_effect, center_x, 200)

            pygame.display.flip()
            pygame.time.wait(50)

        # Phase 4: Show race name and description
        race_name = race_data['name']
        race_description = race_data.get('description', 'asdasd')

        # Format effects
        effects = []
        for stat, value in race_data.get('base_stats', {}).items():
            if value != 0:
                effects.append(f"{stat}: {value}")

        for stat, value in race_data.get('utility_stats', {}).items():
            if isinstance(value, (int, float)) and value != 0:
                effects.append(f"{stat}: {value:+d}")

        if not effects:
            effects = ["no bonus"]

        # Show final race info
        content = [
            race_name,
            "",
            "Effects:",
        ] + effects[:5]  # Limit to first 5 effects

        self.screen.fill(self.bg_color)
        self.draw_left_panel()
        self.draw_right_panel("Race Generated", content)
        self.draw_center_image(race_image)

        # Type out description character by character
        desc_y = SCREEN_HEIGHT - 200
        desc_x = self.left_panel_width + 60
        desc_width = SCREEN_WIDTH - self.left_panel_width - self.right_panel_width - 120

        for i in range(len(race_description) + 1):
            self.screen.fill(self.bg_color)
            self.draw_left_panel()
            self.draw_right_panel("Race Generated", content)
            self.draw_center_image(race_image)

            # Render partial description
            partial_desc = race_description[:i]
            if partial_desc:
                # Word wrap the description
                words = partial_desc.split(' ')
                lines = []
                current_line = ""

                for word in words:
                    test_line = current_line + (" " if current_line else "") + word
                    test_surface = self.fonts['small'].render(test_line, True, self.text_color)
                    if test_surface.get_width() <= desc_width:
                        current_line = test_line
                    else:
                        if current_line:
                            lines.append(current_line)
                        current_line = word

                if current_line:
                    lines.append(current_line)

                # Draw lines
                for j, line in enumerate(lines):
                    line_surface = self.fonts['small'].render(line, True, self.text_color)
                    self.screen.blit(line_surface, (desc_x, desc_y + j * 20))

            pygame.display.flip()
            pygame.time.wait(50)

        # Wait for keypress
        if not self.wait_for_keypress():
            return False

        # Add to generated data
        race_image_small = pygame.transform.scale(race_image, (80, 80))
        self.generated_data.append({
            'type': 'Race',
            'image': race_image_small,
            'details': [
                f"{race_name} ({rarity_text})",
                quality_text,
                f"Effects: {len(effects)} bonuses"
            ]
        })

        return True

    def generate_and_reveal_gender(self) -> bool:
        """Generate and reveal gender with new UI"""
        # Generate gender
        gender_data = self.generator.generate_gender()
        self.character_data = self.generator.character_profile
        self.logger.log_gender_generation(gender_data)

        # Load gender image
        gender_image = self.load_gender_image(gender_data['name'])

        # Phase 1: Show "Generating Gender..."
        self.screen.fill(self.bg_color)
        self.draw_left_panel()
        self.draw_right_panel("Generating Gender...")
        self.draw_center_image(gender_image)
        pygame.display.flip()
        time.sleep(1.5)

        # Phase 2: Show gender name with text effect
        gender_name = gender_data['name'].upper()
        gender_effect = self.create_text_effect(gender_name, self.fonts['title'], self.gold_color, 3.0)

        start_time = time.time()
        while time.time() - start_time < 3.0:
            self.screen.fill(self.bg_color)
            self.draw_left_panel()
            self.draw_right_panel("Gender Generation")
            self.draw_center_image(gender_image)

            # Render gender effect
            center_x = self.left_panel_width + 40 + (SCREEN_WIDTH - self.left_panel_width - self.right_panel_width - 80) // 2
            self.render_text_effect(gender_effect, center_x, 150)

            pygame.display.flip()
            pygame.time.wait(50)

        # Format effects
        effects = []
        for stat, value in gender_data.get('effects', {}).items():
            if isinstance(value, (int, float)) and value != 0:
                if abs(value) < 1:
                    effects.append(f"{value:+.0%} {stat}")
                else:
                    effects.append(f"{value:+d} {stat}")

        if not effects:
            effects = ["no bonus"]

        # Show final gender info
        content = [
            gender_data['name'],
            "",
            "Effects:",
        ] + effects

        self.screen.fill(self.bg_color)
        self.draw_left_panel()
        self.draw_right_panel("Gender Generated", content)
        self.draw_center_image(gender_image)
        pygame.display.flip()

        # Wait for keypress
        if not self.wait_for_keypress():
            return False

        # Add to generated data
        gender_image_small = pygame.transform.scale(gender_image, (50, 50))
        self.generated_data.append({
            'type': 'Gender',
            'image': gender_image_small,
            'details': [
                gender_data['name'],
                f"Effects: {len(effects)} bonuses" if effects != ["no bonus"] else "no bonus"
            ]
        })

        return True

    def run_character_generation(self) -> Optional[Dict[str, Any]]:
        """Run the full-screen character generation process"""
        try:
            # Start logging
            self.logger.start_generation()

            # Generate race
            if not self.generate_and_reveal_race():
                return None

            # Generate gender
            if not self.generate_and_reveal_gender():
                return None

            # TODO: Add DNA traits, background history, etc.

            return self.character_data

        except Exception as e:
            print(f"Character generation error: {e}")
            return None
