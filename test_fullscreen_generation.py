#!/usr/bin/env python3
"""
Test script for the new fullscreen character generation UI
"""

import pygame
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from character_generation.fullscreen_character_generator_ui import FullscreenCharacterGeneratorUI

def main():
    """Test the fullscreen character generation UI"""
    print("Testing Fullscreen Character Generation UI...")
    
    # Initialize pygame
    pygame.init()
    
    # Set up display
    screen = pygame.display.set_mode((1280, 720))
    pygame.display.set_caption("One Life Isekai - Fullscreen Character Generation Test")
    
    try:
        # Create and run the fullscreen character generator
        char_gen_ui = FullscreenCharacterGeneratorUI(screen)
        character = char_gen_ui.run_character_generation()
        
        if character:
            print("✅ Character generation completed successfully!")
            
            # Display character summary
            race = character['race']
            gender = character['gender']
            print(f"\n📜 Generated Character:")
            print(f"   Race: {race['name']} ({race['rarity']}, {race['quality']})")
            print(f"   Gender: {gender['name']}")
            
            # Show key stats if available
            if 'effective_stats' in character:
                stats = character['effective_stats']
                print(f"   Key Stats: STR={stats.get('STR', 0)}, DEX={stats.get('DEX', 0)}, INT={stats.get('INT', 0)}")
                print(f"   Magic Affinity: {stats.get('magic_affinity', 0)}")
                print(f"   Beauty: {stats.get('beauty', 0)}")
        else:
            print("❌ Character generation was cancelled or failed.")
    
    except Exception as e:
        print(f"❌ Error during character generation: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        pygame.quit()

if __name__ == "__main__":
    main()
