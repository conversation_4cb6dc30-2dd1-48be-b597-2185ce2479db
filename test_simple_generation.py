#!/usr/bin/env python3
"""
Simple test script for character generation components
"""

import pygame
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test if all imports work correctly"""
    print("Testing imports...")
    
    try:
        from character_generation.character_generator import CharacterGenerator
        print("✅ CharacterGenerator import successful")
        
        from character_generation.character_logger import CharacterLogger
        print("✅ CharacterLogger import successful")
        
        from character_generation.name_generator import NameGenerator
        print("✅ NameGenerator import successful")
        
        from character_generation.races import RACES
        print("✅ RACES import successful")
        
        from character_generation.fullscreen_character_generator_ui import FullscreenCharacterGeneratorUI
        print("✅ FullscreenCharacterGeneratorUI import successful")
        
        return True
    except Exception as e:
        print(f"❌ Import error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_basic_generation():
    """Test basic character generation without UI"""
    print("\nTesting basic character generation...")
    
    try:
        from character_generation.character_generator import CharacterGenerator
        
        generator = CharacterGenerator()
        
        # Test race generation
        race_data = generator.generate_race()
        print(f"✅ Race generated: {race_data['name']} ({race_data['rarity']}, {race_data['quality']})")
        
        # Test gender generation
        gender_data = generator.generate_gender()
        print(f"✅ Gender generated: {gender_data['name']}")
        
        return True
    except Exception as e:
        print(f"❌ Generation error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_creation():
    """Test UI creation without running the full generation"""
    print("\nTesting UI creation...")
    
    try:
        # Initialize pygame
        pygame.init()
        screen = pygame.display.set_mode((1280, 720))
        
        from character_generation.fullscreen_character_generator_ui import FullscreenCharacterGeneratorUI
        
        # Create UI
        ui = FullscreenCharacterGeneratorUI(screen)
        print("✅ FullscreenCharacterGeneratorUI created successfully")
        
        # Test image loading
        race_image = ui.load_race_image("Human")
        print(f"✅ Race image loaded: {race_image.get_size()}")
        
        gender_image = ui.load_gender_image("Male")
        print(f"✅ Gender image loaded: {gender_image.get_size()}")
        
        pygame.quit()
        return True
    except Exception as e:
        print(f"❌ UI creation error: {e}")
        import traceback
        traceback.print_exc()
        pygame.quit()
        return False

def main():
    """Run all tests"""
    print("=" * 50)
    print("SIMPLE CHARACTER GENERATION TESTS")
    print("=" * 50)
    
    # Test imports
    if not test_imports():
        return 1
    
    # Test basic generation
    if not test_basic_generation():
        return 1
    
    # Test UI creation
    if not test_ui_creation():
        return 1
    
    print("\n✅ All tests passed!")
    print("The fullscreen character generator should work correctly.")
    return 0

if __name__ == "__main__":
    sys.exit(main())
